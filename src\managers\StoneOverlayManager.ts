import { Gem } from '../objects/Gem';

/**
 * StoneOverlayManager - Quản lý stone overlay system
 * Handles creation, removal, và tracking của stone overlays
 */
export class StoneOverlayManager {
    private scene: Phaser.Scene;
    private stoneOverlayCount: number = 0;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.setupEventListeners();
    }

    /**
     * Setup event listeners cho stone overlay tracking
     */
    private setupEventListeners(): void {
        this.scene.events.on('stone-overlay-added', this.onStoneOverlayAdded, this);
        this.scene.events.on('stone-overlay-removed', this.onStoneOverlayRemoved, this);
    }

    /**
     * Handle khi stone overlay được thêm
     */
    private onStoneOverlayAdded(data: { gem: Gem }): void {
        this.stoneOverlayCount++;
        console.log(`Stone overlay added. Total: ${this.stoneOverlayCount}`);
    }

    /**
     * Handle khi stone overlay bị remove
     */
    private onStoneOverlayRemoved(data: { gem: Gem }): void {
        this.stoneOverlayCount--;
        console.log(`Stone overlay removed. Total: ${this.stoneOverlayCount}`);
        
        // Emit event cho objective tracking
        this.scene.events.emit('stone-overlay-destroyed', { gem: data.gem });
    }

    /**
     * Add stone overlay to specific gem
     */
    public addStoneOverlayToGem(gem: Gem): boolean {
        if (!gem || gem.hasStoneOverlay()) {
            return false;
        }

        gem.addStoneOverlay();
        return true;
    }

    /**
     * Remove stone overlay from specific gem
     */
    public async removeStoneOverlayFromGem(gem: Gem): Promise<boolean> {
        if (!gem || !gem.hasStoneOverlay()) {
            return false;
        }

        await gem.removeStoneOverlay();
        return true;
    }

    /**
     * Add stone overlays to random gems trong grid
     */
    public addRandomStoneOverlays(gems: (Gem | null)[][], count: number): number {
        const availableGems: Gem[] = [];
        
        // Collect all valid gems (không có overlay và không phải special gems)
        for (let row = 0; row < gems.length; row++) {
            for (let col = 0; col < gems[row].length; col++) {
                const gem = gems[row][col];
                if (gem && !gem.hasStoneOverlay() && !gem.isSpecial()) {
                    availableGems.push(gem);
                }
            }
        }

        // Shuffle array để random selection
        for (let i = availableGems.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [availableGems[i], availableGems[j]] = [availableGems[j], availableGems[i]];
        }

        // Add overlays to first 'count' gems
        const actualCount = Math.min(count, availableGems.length);
        for (let i = 0; i < actualCount; i++) {
            this.addStoneOverlayToGem(availableGems[i]);
        }

        return actualCount;
    }

    /**
     * Add stone overlays to specific positions
     */
    public addStoneOverlaysAtPositions(gems: (Gem | null)[][], positions: { row: number, col: number }[]): number {
        let addedCount = 0;

        positions.forEach(pos => {
            if (pos.row >= 0 && pos.row < gems.length && 
                pos.col >= 0 && pos.col < gems[pos.row].length) {
                
                const gem = gems[pos.row][pos.col];
                if (gem && this.addStoneOverlayToGem(gem)) {
                    addedCount++;
                }
            }
        });

        return addedCount;
    }

    /**
     * Get current stone overlay count
     */
    public getStoneOverlayCount(): number {
        return this.stoneOverlayCount;
    }

    /**
     * Check if gem có thể có stone overlay
     */
    public canHaveStoneOverlay(gem: Gem): boolean {
        return gem && !gem.hasStoneOverlay() && !gem.isSpecial();
    }

    /**
     * Get all gems với stone overlays trong grid
     */
    public getGemsWithStoneOverlays(gems: (Gem | null)[][]): Gem[] {
        const stoneOverlayGems: Gem[] = [];

        for (let row = 0; row < gems.length; row++) {
            for (let col = 0; col < gems[row].length; col++) {
                const gem = gems[row][col];
                if (gem && gem.hasStoneOverlay()) {
                    stoneOverlayGems.push(gem);
                }
            }
        }

        return stoneOverlayGems;
    }

    /**
     * Remove all stone overlays from grid
     */
    public async removeAllStoneOverlays(gems: (Gem | null)[][]): Promise<void> {
        const stoneOverlayGems = this.getGemsWithStoneOverlays(gems);
        
        // Remove all overlays in parallel
        await Promise.all(stoneOverlayGems.map(gem => this.removeStoneOverlayFromGem(gem)));
    }

    /**
     * Reset stone overlay count (for level restart)
     */
    public reset(): void {
        this.stoneOverlayCount = 0;
    }

    /**
     * Cleanup khi destroy manager
     */
    public destroy(): void {
        this.scene.events.off('stone-overlay-added', this.onStoneOverlayAdded, this);
        this.scene.events.off('stone-overlay-removed', this.onStoneOverlayRemoved, this);
    }
}
