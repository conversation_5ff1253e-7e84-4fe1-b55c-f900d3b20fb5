/**
 * Utility class để tạo placeholder assets cho gems
 * Sẽ được thay thế bằng custom images sau
 */
export class AssetGenerator {
    
    /**
     * Tạo texture cho gem với màu sắc khác nhau
     */
    static createGemTexture(scene: Phaser.Scene, color: number, type: string): void {
        const graphics = scene.add.graphics();
        
        // Tạo hình kim cương
        graphics.fillStyle(color);
        graphics.beginPath();
        graphics.moveTo(64, 20);  // Top
        graphics.lineTo(100, 64); // Right
        graphics.lineTo(64, 108); // Bottom
        graphics.lineTo(28, 64);  // Left
        graphics.closePath();
        graphics.fillPath();
        
        // Thêm highlight
        graphics.fillStyle(0xffffff, 0.3);
        graphics.beginPath();
        graphics.moveTo(64, 20);
        graphics.lineTo(80, 40);
        graphics.lineTo(64, 60);
        graphics.lineTo(48, 40);
        graphics.closePath();
        graphics.fillPath();
        
        // Tạo texture từ graphics
        graphics.generateTexture(type, 128, 128);
        graphics.destroy();
    }
    
    /**
     * <PERSON><PERSON><PERSON> tất cả gem textures
     */
    static createAllGemTextures(scene: Phaser.Scene): void {
        const gemTypes = [
            // Basic gems
            { type: 'gem_red', color: 0xff4444 },
            { type: 'gem_blue', color: 0x4444ff },
            { type: 'gem_green', color: 0x44ff44 },
            { type: 'gem_yellow', color: 0xffff44 },
            { type: 'gem_purple', color: 0xff44ff },
            { type: 'gem_orange', color: 0xff8844 },
            { type: 'gem_white', color: 0xffffff },
            // Advanced gems
            { type: 'gem_diamond', color: 0xe6f3ff },
            { type: 'gem_ruby', color: 0xcc0000 },
            { type: 'gem_emerald', color: 0x00cc66 },
            { type: 'gem_sapphire', color: 0x0066cc },
            { type: 'gem_crystal', color: 0xccccff },
            { type: 'gem_moonstone', color: 0xf0f8ff },
            { type: 'gem_obsidian', color: 0x1a1a1a },
            { type: 'gem_fire_opal', color: 0xff6600 },
            { type: 'gem_ice_crystal', color: 0x99ddff },
            { type: 'gem_lightning', color: 0xffff00 }
        ];

        gemTypes.forEach(gem => {
            this.createGemTexture(scene, gem.color, gem.type);
        });

        // Create special gem textures
        this.createAllSpecialGemTextures(scene);

        // Create power-up textures
        this.createAllPowerUpTextures(scene);

        // Create star texture
        this.createStarTexture(scene);
    }

    /**
     * Tạo striped gem texture
     */
    static createStripedGemTexture(scene: Phaser.Scene, color: number, type: string, direction: 'horizontal' | 'vertical'): void {
        const graphics = scene.add.graphics();

        // Tạo base gem shape
        graphics.fillStyle(color);
        graphics.beginPath();
        graphics.moveTo(64, 20);  // Top
        graphics.lineTo(100, 64); // Right
        graphics.lineTo(64, 108); // Bottom
        graphics.lineTo(28, 64);  // Left
        graphics.closePath();
        graphics.fillPath();

        // Add stripes
        graphics.fillStyle(0xffffff, 0.8);
        if (direction === 'horizontal') {
            // Horizontal stripes
            for (let i = 0; i < 3; i++) {
                const y = 35 + (i * 20);
                graphics.fillRect(30, y, 68, 6);
            }
        } else {
            // Vertical stripes
            for (let i = 0; i < 3; i++) {
                const x = 45 + (i * 15);
                graphics.fillRect(x, 25, 6, 78);
            }
        }

        // Tạo texture từ graphics
        graphics.generateTexture(type, 128, 128);
        graphics.destroy();
    }

    /**
     * Tạo wrapped gem texture
     */
    static createWrappedGemTexture(scene: Phaser.Scene, color: number, type: string): void {
        const graphics = scene.add.graphics();

        // Tạo base gem shape
        graphics.fillStyle(color);
        graphics.beginPath();
        graphics.moveTo(64, 20);  // Top
        graphics.lineTo(100, 64); // Right
        graphics.lineTo(64, 108); // Bottom
        graphics.lineTo(28, 64);  // Left
        graphics.closePath();
        graphics.fillPath();

        // Add wrapping effect (cross pattern)
        graphics.lineStyle(8, 0xffffff, 0.9);
        graphics.beginPath();
        graphics.moveTo(40, 40);
        graphics.lineTo(88, 88);
        graphics.moveTo(88, 40);
        graphics.lineTo(40, 88);
        graphics.strokePath();

        // Add center glow
        graphics.fillStyle(0xffffff, 0.6);
        graphics.fillCircle(64, 64, 12);

        // Tạo texture từ graphics
        graphics.generateTexture(type, 128, 128);
        graphics.destroy();
    }

    /**
     * Tạo color bomb texture
     */
    static createColorBombTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();

        // Tạo base sphere
        graphics.fillGradientStyle(0x000000, 0x333333, 0x000000, 0x333333, 1);
        graphics.fillCircle(64, 64, 40);

        // Add rainbow stripes
        const colors = [0xff0000, 0xff8800, 0xffff00, 0x00ff00, 0x0088ff, 0x8800ff];
        for (let i = 0; i < colors.length; i++) {
            graphics.fillStyle(colors[i], 0.8);
            const angle = (i * 60) * Math.PI / 180;
            const x1 = 64 + Math.cos(angle) * 35;
            const y1 = 64 + Math.sin(angle) * 35;
            const x2 = 64 + Math.cos(angle + Math.PI) * 35;
            const y2 = 64 + Math.sin(angle + Math.PI) * 35;
            graphics.fillRect(x1 - 3, y1 - 3, 6, Math.sqrt((x2-x1)*(x2-x1) + (y2-y1)*(y2-y1)));
        }

        // Add sparkle effect
        graphics.fillStyle(0xffffff, 0.9);
        for (let i = 0; i < 8; i++) {
            const angle = (i * 45) * Math.PI / 180;
            const x = 64 + Math.cos(angle) * 25;
            const y = 64 + Math.sin(angle) * 25;
            graphics.fillCircle(x, y, 2);
        }

        // Tạo texture từ graphics
        graphics.generateTexture('gem_color_bomb', 128, 128);
        graphics.destroy();
    }

    /**
     * Tạo tất cả special gem textures
     */
    static createAllSpecialGemTextures(scene: Phaser.Scene): void {
        console.log('Creating special gem textures...');

        const gemTypes = [
            { base: 'red', color: 0xff4444 },
            { base: 'blue', color: 0x4444ff },
            { base: 'green', color: 0x44ff44 },
            { base: 'yellow', color: 0xffff44 },
            { base: 'purple', color: 0xff44ff },
            { base: 'orange', color: 0xff8844 },
            { base: 'white', color: 0xffffff }
        ];

        // Create striped gems
        gemTypes.forEach(gem => {
            try {
                this.createStripedGemTexture(scene, gem.color, `gem_${gem.base}_striped_h`, 'horizontal');
                this.createStripedGemTexture(scene, gem.color, `gem_${gem.base}_striped_v`, 'vertical');
                this.createWrappedGemTexture(scene, gem.color, `gem_${gem.base}_wrapped`);
                console.log(`Created special textures for ${gem.base}`);
            } catch (error) {
                console.error(`Failed to create special textures for ${gem.base}:`, error);
            }
        });

        // Create color bomb (only one needed)
        try {
            this.createColorBombTexture(scene);
            console.log('Created color bomb texture');
        } catch (error) {
            console.error('Failed to create color bomb texture:', error);
        }

        console.log('Special gem texture creation completed');
    }
    
    /**
     * Tạo background texture
     */
    static createBackgroundTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Gradient background
        graphics.fillGradientStyle(0x1a1a2e, 0x16213e, 0x0f3460, 0x533483, 1);
        graphics.fillRect(0, 0, 1024, 768);
        
        graphics.generateTexture('background', 1024, 768);
        graphics.destroy();
    }
    
    /**
     * Tạo grid cell texture (updated for smaller size with clear borders)
     */
    static createGridCellTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        const cellSize = 60;

        // Clear background
        graphics.fillStyle(0x000000, 0);
        graphics.fillRect(0, 0, cellSize, cellSize);

        // Strong outer border (very visible)
        graphics.lineStyle(2, 0x8B4513, 1); // Brown border like the frame
        graphics.strokeRect(1, 1, cellSize - 2, cellSize - 2);

        // Inner cell background (semi-transparent)
        graphics.fillStyle(0x2a2a3e, 0.3);
        graphics.fillRect(2, 2, cellSize - 4, cellSize - 4);

        // Inner border for more definition
        graphics.lineStyle(1, 0xA0522D, 0.8);
        graphics.strokeRect(3, 3, cellSize - 6, cellSize - 6);

        graphics.generateTexture('grid_cell', cellSize, cellSize);
        graphics.destroy();
    }

    /**
     * Tạo board frame texture (wooden frame like in the image)
     */
    static createBoardFrameTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        const frameSize = 520; // Total frame size
        const frameThickness = 8;
        const innerSize = frameSize - (frameThickness * 2);

        // Outer frame (darker wood)
        graphics.fillStyle(0x8B4513, 1);
        graphics.fillRoundedRect(0, 0, frameSize, frameSize, 12);

        // Inner bevel highlight
        graphics.fillStyle(0xCD853F, 1);
        graphics.fillRoundedRect(2, 2, frameSize - 4, frameSize - 4, 10);

        // Inner frame (medium wood)
        graphics.fillStyle(0xA0522D, 1);
        graphics.fillRoundedRect(4, 4, frameSize - 8, frameSize - 8, 8);

        // Inner cutout for game board
        graphics.fillStyle(0x000000, 0);
        graphics.fillRoundedRect(frameThickness, frameThickness, innerSize, innerSize, 4);

        // Add wood grain effect with subtle lines
        graphics.lineStyle(1, 0x654321, 0.3);
        for (let i = 10; i < frameSize - 10; i += 8) {
            graphics.beginPath();
            graphics.moveTo(i, 4);
            graphics.lineTo(i + 2, frameThickness - 2);
            graphics.stroke();

            graphics.beginPath();
            graphics.moveTo(4, i);
            graphics.lineTo(frameThickness - 2, i + 2);
            graphics.stroke();
        }

        graphics.generateTexture('board_frame', frameSize, frameSize);
        graphics.destroy();
    }
    
    /**
     * Tạo selection highlight texture
     */
    static createSelectionTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        
        // Glowing border
        graphics.lineStyle(4, 0xffff00, 1);
        graphics.strokeRoundedRect(0, 0, 64, 64, 8);
        
        // Inner glow
        graphics.lineStyle(2, 0xffffff, 0.8);
        graphics.strokeRoundedRect(2, 2, 60, 60, 6);
        
        graphics.generateTexture('selection', 64, 64);
        graphics.destroy();
    }
    
    /**
     * Tạo particle texture cho effects
     */
    static createParticleTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();

        // Small sparkle
        graphics.fillStyle(0xffffff);
        graphics.fillCircle(8, 8, 6);

        // Inner bright spot
        graphics.fillStyle(0xffff88);
        graphics.fillCircle(8, 8, 3);

        graphics.generateTexture('particle', 16, 16);
        graphics.destroy();
    }

    /**
     * Tạo power-up textures
     */
    static createAllPowerUpTextures(scene: Phaser.Scene): void {
        const powerUps = [
            { type: 'power_up_1', color: 0x44ffff, symbol: '?' },     // Hint - Cyan
            { type: 'power_up_2', color: 0x888888, symbol: '🔨' },    // Hammer - Gray
            { type: 'power_up_3', color: 0xff4444, symbol: '💣' },    // Bomb - Red
            { type: 'power_up_4', color: 0xffff44, symbol: '⚡' },    // Lightning - Yellow
            { type: 'power_up_5', color: 0x44ff44, symbol: '🔀' },    // Shuffle - Green
            { type: 'power_up_6', color: 0xff44ff, symbol: '🌟' }     // Color Blast - Magenta
        ];

        powerUps.forEach(powerUp => {
            this.createPowerUpTexture(scene, powerUp.color, powerUp.type, powerUp.symbol);
        });
    }

    /**
     * Tạo individual power-up texture
     */
    static createPowerUpTexture(scene: Phaser.Scene, color: number, type: string, symbol: string): void {
        const graphics = scene.add.graphics();

        // Background circle
        graphics.fillStyle(color);
        graphics.fillCircle(64, 64, 50);

        // Inner highlight
        graphics.fillStyle(0xffffff, 0.3);
        graphics.fillCircle(64, 64, 40);

        // Border
        graphics.lineStyle(4, 0x000000, 0.8);
        graphics.strokeCircle(64, 64, 50);

        graphics.generateTexture(type, 128, 128);
        graphics.destroy();

        // Add text symbol (simplified for generated assets)
        const textGraphics = scene.add.graphics();
        textGraphics.fillStyle(0x000000);
        textGraphics.fillCircle(64, 64, 20);
        textGraphics.generateTexture(`${type}_symbol`, 128, 128);
        textGraphics.destroy();
    }

    /**
     * Tạo star texture
     */
    static createStarTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();

        // Star shape
        graphics.fillStyle(0xffd54f);
        graphics.beginPath();

        // Create 5-pointed star
        const centerX = 64;
        const centerY = 64;
        const outerRadius = 40;
        const innerRadius = 20;

        for (let i = 0; i < 10; i++) {
            const angle = (i * Math.PI) / 5;
            const radius = i % 2 === 0 ? outerRadius : innerRadius;
            const x = centerX + Math.cos(angle - Math.PI / 2) * radius;
            const y = centerY + Math.sin(angle - Math.PI / 2) * radius;

            if (i === 0) {
                graphics.moveTo(x, y);
            } else {
                graphics.lineTo(x, y);
            }
        }

        graphics.closePath();
        graphics.fillPath();

        // Add highlight
        graphics.fillStyle(0xffffff, 0.4);
        graphics.fillCircle(centerX, centerY, 15);

        graphics.generateTexture('star', 128, 128);
        graphics.destroy();
    }

    /**
     * Tạo obstacle texture
     */
    static createObstacleTexture(scene: Phaser.Scene, color: number, type: string): void {
        const graphics = scene.add.graphics();
        const size = 128;
        const padding = 10;
        const innerSize = size - (padding * 2);

        // Outer border (darker)
        graphics.fillStyle(Phaser.Display.Color.GetColor32(
            Phaser.Display.Color.ColorToRGBA(color).r * 0.6,
            Phaser.Display.Color.ColorToRGBA(color).g * 0.6,
            Phaser.Display.Color.ColorToRGBA(color).b * 0.6,
            255
        ));
        graphics.fillRoundedRect(padding, padding, innerSize, innerSize, 8);

        // Inner fill
        graphics.fillStyle(color);
        graphics.fillRoundedRect(padding + 4, padding + 4, innerSize - 8, innerSize - 8, 6);

        // Highlight
        graphics.fillStyle(0xffffff, 0.3);
        graphics.fillRoundedRect(padding + 6, padding + 6, innerSize - 20, innerSize - 20, 4);

        graphics.generateTexture(type, size, size);
        graphics.destroy();
    }

    /**
     * Tạo tất cả obstacle textures
     */
    static createAllObstacleTextures(scene: Phaser.Scene): void {
        const obstacleTypes = [
            { type: 'obstacle_stone', color: 0x808080 },  // Gray
            { type: 'obstacle_wood', color: 0x8B4513 },   // Brown
            { type: 'obstacle_ice', color: 0x87CEEB },    // Light blue
            { type: 'obstacle_metal', color: 0x708090 }   // Slate gray
        ];

        obstacleTypes.forEach(obstacle => {
            this.createObstacleTexture(scene, obstacle.color, obstacle.type);
        });

        console.log('Obstacle textures created successfully');
    }

    /**
     * Tạo stone overlay texture (giống obstacle stone nhưng cho overlay)
     */
    static createStoneOverlayTexture(scene: Phaser.Scene): void {
        const graphics = scene.add.graphics();
        const size = 128;
        const padding = 10;
        const innerSize = size - (padding * 2);

        // Outer border (darker) - giống obstacle stone
        graphics.fillStyle(Phaser.Display.Color.GetColor32(
            Phaser.Display.Color.ColorToRGBA(0x808080).r * 0.6,
            Phaser.Display.Color.ColorToRGBA(0x808080).g * 0.6,
            Phaser.Display.Color.ColorToRGBA(0x808080).b * 0.6,
            255
        ));
        graphics.fillRoundedRect(padding, padding, innerSize, innerSize, 8);

        // Inner fill - stone color
        graphics.fillStyle(0x808080); // Gray stone color
        graphics.fillRoundedRect(padding + 4, padding + 4, innerSize - 8, innerSize - 8, 6);

        // Highlight - giống obstacle stone
        graphics.fillStyle(0xffffff, 0.3);
        graphics.fillRoundedRect(padding + 6, padding + 6, innerSize - 20, innerSize - 20, 4);

        // Add stone texture pattern
        graphics.lineStyle(1, 0x666666, 0.5);
        // Crack pattern
        for (let i = 0; i < 3; i++) {
            const x1 = padding + 15 + (i * 20);
            const y1 = padding + 10;
            const x2 = x1 + 10;
            const y2 = y1 + innerSize - 20;
            graphics.beginPath();
            graphics.moveTo(x1, y1);
            graphics.lineTo(x2, y2);
            graphics.stroke();
        }

        graphics.generateTexture('stone_overlay', size, size);
        graphics.destroy();
    }
}
