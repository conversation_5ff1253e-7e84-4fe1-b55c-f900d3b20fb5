# 🎮 Power-ups & Special Gems System Documentation

## 📋 Tổng quan

Game Match 3 có 2 hệ thống power chính:
- **Special Gems**: <PERSON><PERSON><PERSON><PERSON> tạo tự động từ matches đặc biệt (4+ gems, L/T shapes)
- **Power-ups**: Tools có thể sử dụng bằng tay, có inventory và cooldown system

---

## ⭐ Special Gems System

### 🔸 Các loại Special Gems

| Loại | Enum | Điều kiện tạo | Hiệu ứng |
|------|------|---------------|----------|
| **Striped Horizontal** | `STRIPED_HORIZONTAL` | Match 4 gems theo chiều dọc | Phá toàn bộ hàng ngang |
| **Striped Vertical** | `STRIPED_VERTICAL` | Match 4 gems theo chiều ngang | Phá toàn bộ cột dọc |
| **Wrapped** | `WRAPPED` | L-shape hoặc T-shape match | Phá vùng 3x3 xung quanh |
| **Color Bomb** | `COLOR_BOMB` | Match 5+ gems | Phá tất cả gems cùng màu |

### 🎨 Assets Special Gems

**Đường dẫn**: `public/assets/images/`
- `special_gem_1.webp` → Striped Horizontal
- `special_gem_2.webp` → Striped Vertical  
- `special_gem_3.webp` → Wrapped
- `special_gem_4.webp` → Color Bomb

**Specs**: 128x128px, WebP format, transparent background

### 🔧 Technical Implementation

**File**: `src/types/GemTypes.ts`
```typescript
enum SpecialGemType {
    NONE = 'none',
    STRIPED_HORIZONTAL = 'striped_horizontal',
    STRIPED_VERTICAL = 'striped_vertical', 
    WRAPPED = 'wrapped',
    COLOR_BOMB = 'color_bomb'
}
```

**Match Detection Logic** (`src/managers/GameBoard.ts`):
- **4-match horizontal** → Striped Vertical
- **4-match vertical** → Striped Horizontal
- **5+ match** → Color Bomb
- **L/T shapes** → Wrapped

### ✨ Visual Effects

**Glow Effects** (`src/objects/Gem.ts`):
- Striped: White glow (`0xffffff`)
- Wrapped: Yellow glow (`0xffff00`)
- Color Bomb: Magenta glow (`0xff00ff`)
- Animation: Alpha pulse 0.3-0.8, 1000ms duration

**Activation Effects** (`src/managers/ParticleManager.ts`):
- Horizontal line: Particle beam across row
- Vertical line: Particle beam down column
- Explosion: 3x3 burst effect
- Color bomb: Screen-wide flash + multi-color particles

---

## 🛠️ Power-ups System

### 🔸 Các loại Power-ups

| Power-up | Icon | Hiệu ứng | Cooldown | Starting Qty |
|----------|------|----------|----------|--------------|
| **Hint** | `power_up_1` | Highlight possible moves | 0 moves | ∞ |
| **Hammer** | `power_up_2` | Destroy single gem | 1 move | 5 |
| **Bomb** | `power_up_3` | Destroy 5x5 area | 3 moves | 3 |
| **Lightning** | `power_up_4` | Destroy row + column | 2 moves | 2 |
| **Shuffle** | `power_up_5` | Reshuffle board | 5 moves | 1 |
| **Color Blast** | `power_up_6.webp` | Destroy all gems of target color | 4 moves | 1 |

### 🎨 Assets Power-ups

**Đường dẫn**: `public/assets/images/`
- Files: `power_up_1.webp` đến `power_up_6.webp`
- **Specs**: 128x128px, WebP format, transparent background
- **Fallback**: Generated textures với emoji symbols

### 🔧 Technical Implementation

**File**: `src/types/PowerUpTypes.ts`
```typescript
enum PowerUpType {
    BOMB = 'bomb',
    LIGHTNING = 'lightning', 
    HAMMER = 'hammer',
    SHUFFLE = 'shuffle',
    COLOR_BLAST = 'color_blast',
    HINT = 'hint'
}
```

**Inventory System** (`src/managers/PowerUpManager.ts`):
- Move-based cooldown system
- Quantity tracking
- Usage validation
- UI state management

### 🎯 Power-up Effects

**File**: `src/effects/PowerUpEffects.ts`

**Bomb Effect**:
- Target: 5x5 grid around click position
- Validation: Must click on valid gem
- Animation: Explosion particles + screen shake

**Lightning Effect**:
- Target: Entire row + column of clicked gem
- Visual: Cross-shaped particle beams
- Audio: `power_up_use` sound

**Hammer Effect**:
- Target: Single gem
- Simple destruction with dust particles
- Most basic power-up

**Shuffle Effect**:
- Randomizes all gems on board
- Maintains board shape constraints
- Prevents impossible board states

**Color Blast Effect**:
- Destroys all gems matching target color
- Cascading destruction animation
- High-impact visual feedback

---

## 🎵 Audio System

**File**: `src/managers/AudioManager.ts`

**Special Gem Sounds**:
- `special_gem_activate.mp3` - Khi activate special gem
- `match_small/medium/large.mp3` - Dựa trên số gems destroyed

**Power-up Sounds**:
- `power_up_use.mp3` - Khi sử dụng power-up
- `power_up_collect.mp3` - Khi nhận power-up mới

**Combo Sounds**:
- `combo_low.mp3` - Combo 2-3
- `combo_high.mp3` - Combo 4+

---

## 🎨 Particle Effects System

**File**: `src/managers/ParticleManager.ts`

### Background Effects
- Floating sparkles: Continuous ambient particles
- Colors: White, yellow, cyan, magenta
- Depth: -10 (behind everything)

### Match Effects  
- Gem trails: When gems move/fall
- Combo bursts: Multi-colored explosions
- Screen flash: For major effects

### Special Effects
- **Horizontal/Vertical lines**: Beam particles across board
- **Explosions**: Radial burst patterns
- **Color bomb**: Screen-wide celebration
- **Stone breaking**: Dust + sparkle combination

---

## 🖥️ UI/UX System

**File**: `src/ui/PowerUpPanel.ts`

### Power-up Panel
- **Layout**: Horizontal row, 6 buttons
- **Position**: Top of screen, centered
- **Spacing**: 80px between buttons
- **Size**: 60x60px buttons

### Button States
- **Normal**: Gray background (`0x333333`)
- **Selected**: Blue border (`0x4444ff`)
- **Disabled**: Dark gray (`0x222222`), 30% alpha
- **Hover**: Scale 1.05, light border

### Cooldown Display
- **Visual**: Red overlay with move count
- **Text**: "X moves" remaining
- **Update**: On every player move

### Quantity Display
- **Position**: Bottom-right of button
- **Style**: Small white text
- **Behavior**: Hide when quantity = 0

---

## 🔄 Asset Loading System

**File**: `src/game/scenes/Preloader.ts`

### Primary: Sprite Atlas
- **File**: `public/assets/game_atlas.png` + `.json`
- **Tool**: Free Texture Packer
- **Benefits**: Single HTTP request, optimized loading
- **Fallback**: Generated textures if atlas fails

### Secondary: Individual Files
- **Path**: `public/assets/images/`
- **Format**: WebP for compression
- **Loading**: Automatic fallback system

### Generated Assets
- **When**: Atlas unavailable or missing files
- **Quality**: Procedural generation với colors/symbols
- **Performance**: Runtime creation, cached

---

## 🚀 Performance Optimizations

### Memory Management
- **Particle cleanup**: Auto-destroy after lifespan
- **Sound tracking**: Remove completed sounds from memory
- **Texture reuse**: Atlas frames vs individual textures

### Animation Efficiency
- **Tweens**: Phaser's optimized tween system
- **Staggered effects**: Prevent frame drops
- **Depth sorting**: Minimize render calls

### Event System
- **Decoupled**: Managers communicate via events
- **Cleanup**: Remove listeners on scene destroy
- **Validation**: Prevent duplicate activations

---

## 🐛 Common Issues & Solutions

### Asset Loading
- **Problem**: Missing webp files
- **Solution**: Automatic fallback to generated textures
- **Check**: Console logs for loading status

### Power-up Activation
- **Problem**: Multiple activations
- **Solution**: `isActivating` flag prevents spam
- **Validation**: Check cooldown + quantity before use

### Special Gem Creation
- **Problem**: Overlapping special gems
- **Solution**: Position validation before creation
- **Priority**: First valid match gets special gem

### Audio Issues
- **Problem**: Overlapping sounds
- **Solution**: Stop previous sounds before new ones
- **Cleanup**: Track and remove completed sounds

---

## 📝 Development Notes

### Code Organization
- **Types**: `src/types/` - Enums và interfaces
- **Managers**: `src/managers/` - Game logic
- **Effects**: `src/effects/` - Visual/audio effects
- **UI**: `src/ui/` - Interface components

### Testing Checklist
- [ ] All power-ups activate correctly
- [ ] Special gems create from proper matches
- [ ] Cooldown system works
- [ ] Assets load with fallbacks
- [ ] Audio plays without overlap
- [ ] UI updates reflect game state
- [ ] Performance stable during effects

### Future Enhancements
- **New power-ups**: Freeze, Time Stop, Double Score
- **Special combinations**: Special + Special interactions
- **Upgrade system**: Power-up level progression
- **Visual polish**: More particle variety
