/**
 * Class quản lý hệ thống điểm số và combo system
 * Note: Level progression và move management đ<PERSON> chuyển sang LevelManager
 */
export class ScoreManager {
    private scene: Phaser.Scene;
    private currentScore: number = 0;
    private highScore: number = 0;
    private comboMultiplier: number = 1;
    private comboCount: number = 0;
    
    // Score values cho different match types
    private readonly SCORE_VALUES = {
        MATCH_3: 100,
        MATCH_4: 200,
        MATCH_5: 400,
        MATCH_L: 300,
        MATCH_T: 350,
        COMBO_MULTIPLIER: 0.5
    };
    
    // Events
    private onScoreUpdate?: (score: number) => void;
    
    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.loadHighScore();
        this.setupEventListeners();
    }
    
    /**
     * Load high score từ localStorage
     */
    private loadHighScore(): void {
        const saved = localStorage.getItem('match3_highscore');
        if (saved) {
            this.highScore = parseInt(saved, 10);
        }
    }
    
    /**
     * Save high score vào localStorage
     */
    private saveHighScore(): void {
        localStorage.setItem('match3_highscore', this.highScore.toString());
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.on('combo-broken', this.onComboBroken, this);
    }
    
    /**
     * Xử lý khi có matches được processed
     */
    private onMatchesProcessed(matchData: { matches: any[], isCombo: boolean }): void {
        const { matches, isCombo } = matchData;
        
        if (isCombo) {
            this.comboCount++;
            this.comboMultiplier = 1 + (this.comboCount * this.SCORE_VALUES.COMBO_MULTIPLIER);
        } else {
            this.comboCount = 0;
            this.comboMultiplier = 1;
        }
        
        const baseScore = this.calculateMatchScore(matches);
        const finalScore = Math.floor(baseScore * this.comboMultiplier);
        
        this.addScore(finalScore);
        
        // Emit score update event
        this.scene.events.emit('score-updated', {
            score: this.currentScore,
            addedScore: finalScore,
            combo: this.comboCount,
            multiplier: this.comboMultiplier
        });

        // Emit combo event for particle effects
        if (this.comboCount > 0) {
            this.scene.events.emit('combo-achieved', {
                comboCount: this.comboCount,
                x: this.scene.cameras.main.centerX,
                y: this.scene.cameras.main.centerY
            });
        }
    }
    
    /**
     * Tính điểm cho matches
     */
    private calculateMatchScore(matches: any[]): number {
        let totalScore = 0;
        
        // Group matches by type
        const matchGroups = this.groupMatchesByType(matches);
        
        matchGroups.forEach(group => {
            const size = group.length;
            let baseValue = this.SCORE_VALUES.MATCH_3;
            
            if (size === 4) {
                baseValue = this.SCORE_VALUES.MATCH_4;
            } else if (size === 5) {
                baseValue = this.SCORE_VALUES.MATCH_5;
            } else if (size > 5) {
                baseValue = this.SCORE_VALUES.MATCH_5 + ((size - 5) * 100);
            }
            
            // Check for special patterns (L-shape, T-shape)
            if (this.isSpecialPattern(group)) {
                baseValue += this.SCORE_VALUES.MATCH_L;
            }
            
            totalScore += baseValue;
        });
        
        return totalScore;
    }
    
    /**
     * Group matches by connected components
     */
    private groupMatchesByType(matches: any[]): any[][] {
        // Simple grouping - trong thực tế có thể cần logic phức tạp hơn
        // để detect L-shapes, T-shapes, etc.
        return [matches]; // Placeholder implementation
    }
    
    /**
     * Check if match group forms special pattern
     */
    private isSpecialPattern(group: any[]): boolean {
        // Placeholder - implement logic để detect L-shape, T-shape
        return group.length >= 5;
    }
    
    /**
     * Add score (level progression removed - handled by LevelManager)
     */
    private addScore(points: number): void {
        this.currentScore += points;

        // Update high score
        if (this.currentScore > this.highScore) {
            this.highScore = this.currentScore;
            this.saveHighScore();
        }

        // Trigger score update callback
        if (this.onScoreUpdate) {
            this.onScoreUpdate(this.currentScore);
        }
    }
    

    
    /**
     * Reset combo khi không có matches liên tiếp
     */
    private onComboBroken(): void {
        this.comboCount = 0;
        this.comboMultiplier = 1;
    }
    

    
    /**
     * Reset game state (only score and combo)
     */
    public resetGame(): void {
        this.currentScore = 0;
        this.comboCount = 0;
        this.comboMultiplier = 1;
    }
    
    // Getters
    public getCurrentScore(): number { return this.currentScore; }
    public getHighScore(): number { return this.highScore; }
    public getComboMultiplier(): number { return this.comboMultiplier; }
    public getComboCount(): number { return this.comboCount; }

    // Event setters
    public setOnScoreUpdate(callback: (score: number) => void): void {
        this.onScoreUpdate = callback;
    }
    
    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.off('combo-broken', this.onComboBroken, this);
    }
}
