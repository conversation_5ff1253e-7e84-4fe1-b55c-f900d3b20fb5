import { PowerUpType, PowerUpActivationData } from '../types/PowerUpTypes';
import { Gem } from '../objects/Gem';

/**
 * Class quản lý power-up effects
 */
export class PowerUpEffects {
    private scene: Phaser.Scene;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
    }

    /**
     * Execute power-up effect
     */
    public async executePowerUpEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        switch (data.type) {
            case PowerUpType.BOMB:
                return this.executeBombEffect(data, getGemAt, gridSize);
            case PowerUpType.LIGHTNING:
                return this.executeLightningEffect(data, getGemAt, gridSize);
            case PowerUpType.HAMMER:
                return this.executeHammerEffect(data, getGemAt);
            case PowerUpType.SHUFFLE:
                return this.executeShuffleEffect(data, getGemAt, gridSize);
            case PowerUpType.COLOR_BLAST:
                return this.executeColorBlastEffect(data, getGemAt, gridSize);
            case PowerUpType.HINT:
                return this.executeHintEffect(data, getGemAt, gridSize);
            default:
                return [];
        }
    }

    /**
     * Bomb effect - destroys 5x5 area
     */
    private async executeBombEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        if (!data.position) return [];

        const { gridX, gridY } = data.position;
        const gemsToDestroy: Gem[] = [];
        const radius = 2; // 5x5 area (2 cells in each direction)

        // Collect gems trong explosion radius
        for (let row = gridY - radius; row <= gridY + radius; row++) {
            for (let col = gridX - radius; col <= gridX + radius; col++) {
                if (row >= 0 && row < gridSize.rows && col >= 0 && col < gridSize.cols) {
                    const gem = getGemAt(col, row);
                    if (gem) {
                        gemsToDestroy.push(gem);
                    }
                }
            }
        }

        // Play bomb animation
        await this.playBombAnimation(data.position.x, data.position.y, gemsToDestroy);

        return gemsToDestroy;
    }

    /**
     * Lightning effect - destroys entire row and column
     */
    private async executeLightningEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        if (!data.position) return [];

        const { gridX, gridY } = data.position;
        const gemsToDestroy: Gem[] = [];

        // Collect gems trong row
        for (let col = 0; col < gridSize.cols; col++) {
            const gem = getGemAt(col, gridY);
            if (gem) {
                gemsToDestroy.push(gem);
            }
        }

        // Collect gems trong column
        for (let row = 0; row < gridSize.rows; row++) {
            const gem = getGemAt(gridX, row);
            if (gem && !gemsToDestroy.includes(gem)) {
                gemsToDestroy.push(gem);
            }
        }

        // Play lightning animation
        await this.playLightningAnimation(data.position.x, data.position.y, gridX, gridY, gridSize);

        return gemsToDestroy;
    }

    /**
     * Hammer effect - destroys single gem
     */
    private async executeHammerEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null): Promise<Gem[]> {
        if (!data.position) return [];

        const { gridX, gridY } = data.position;
        const gem = getGemAt(gridX, gridY);
        
        if (!gem) return [];

        // Play hammer animation
        await this.playHammerAnimation(data.position.x, data.position.y);

        return [gem];
    }

    /**
     * Shuffle effect - rearranges board
     */
    private async executeShuffleEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        // Shuffle doesn't destroy gems, just rearranges them
        await this.playShuffleAnimation();
        return [];
    }

    /**
     * Color blast effect - destroys all gems of same color
     */
    private async executeColorBlastEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        if (!data.position) return [];

        const { gridX, gridY } = data.position;
        const targetGem = getGemAt(gridX, gridY);
        
        if (!targetGem) return [];

        const targetColor = targetGem.gemType;
        const gemsToDestroy: Gem[] = [];

        // Collect all gems với same color
        for (let row = 0; row < gridSize.rows; row++) {
            for (let col = 0; col < gridSize.cols; col++) {
                const gem = getGemAt(col, row);
                if (gem && gem.gemType === targetColor) {
                    gemsToDestroy.push(gem);
                }
            }
        }

        // Play color blast animation
        await this.playColorBlastAnimation(data.position.x, data.position.y, targetColor);

        return gemsToDestroy;
    }

    /**
     * Hint effect - shows possible moves
     */
    private async executeHintEffect(data: PowerUpActivationData, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Promise<Gem[]> {
        // Find possible moves
        const possibleMoves = this.findPossibleMoves(getGemAt, gridSize);

        if (possibleMoves.length > 0) {
            // Show hint animation for all possible moves
            await this.playHintAnimation(possibleMoves);
        } else {
            // No moves available - show "no moves" message
            await this.playNoMovesAnimation();
        }

        // Hint doesn't destroy gems
        return [];
    }

    /**
     * Play bomb explosion animation
     */
    private async playBombAnimation(x: number, y: number, affectedGems: Gem[]): Promise<void> {
        return new Promise((resolve) => {
            // Create explosion center
            const explosionCenter = this.scene.add.sprite(x, y, 'particle');
            explosionCenter.setScale(0);
            explosionCenter.setTint(0xff4444);
            explosionCenter.setBlendMode(Phaser.BlendModes.ADD);

            // Explosion animation
            this.scene.tweens.add({
                targets: explosionCenter,
                scaleX: 3,
                scaleY: 3,
                alpha: { from: 1, to: 0 },
                duration: 500,
                ease: 'Power2.easeOut',
                onComplete: () => {
                    explosionCenter.destroy();
                }
            });

            // Create shockwave
            const shockwave = this.scene.add.sprite(x, y, 'particle');
            shockwave.setScale(0);
            shockwave.setTint(0xffffff);
            shockwave.setAlpha(0.5);
            shockwave.setBlendMode(Phaser.BlendModes.ADD);

            this.scene.tweens.add({
                targets: shockwave,
                scaleX: 6,
                scaleY: 6,
                alpha: 0,
                duration: 800,
                ease: 'Power2.easeOut',
                onComplete: () => {
                    shockwave.destroy();
                }
            });

            // Explosion particles
            const explosion = this.scene.add.particles(x, y, 'particle', {
                speed: { min: 100, max: 300 },
                scale: { start: 1, end: 0 },
                alpha: { start: 1, end: 0 },
                lifespan: 800,
                quantity: 30,
                angle: { min: 0, max: 360 },
                tint: [0xff4444, 0xff8844, 0xffff44],
                blendMode: 'ADD'
            });

            // Debris particles
            const debris = this.scene.add.particles(x, y, 'particle', {
                speed: { min: 50, max: 150 },
                scale: { start: 0.5, end: 0 },
                alpha: { start: 0.8, end: 0 },
                lifespan: 1200,
                quantity: 20,
                angle: { min: 0, max: 360 },
                tint: [0x888888, 0x666666, 0x444444],
                gravityY: 200
            });

            // Screen shake
            this.scene.cameras.main.shake(300, 0.02);

            // Screen flash
            this.createScreenFlash(0xff4444, 200);

            // Animate affected gems với delay
            affectedGems.forEach((gem, index) => {
                const delay = index * 30;
                this.scene.time.delayedCall(delay, () => {
                    if (gem && gem.scene) {
                        // Add explosion effect to each gem
                        const gemExplosion = this.scene.add.particles(gem.x, gem.y, 'particle', {
                            speed: { min: 50, max: 100 },
                            scale: { start: 0.5, end: 0 },
                            alpha: { start: 1, end: 0 },
                            lifespan: 400,
                            quantity: 5,
                            angle: { min: 0, max: 360 },
                            tint: 0xff4444,
                            blendMode: 'ADD'
                        });

                        this.scene.time.delayedCall(500, () => {
                            gemExplosion.destroy();
                        });
                    }
                });
            });

            // Cleanup particles
            this.scene.time.delayedCall(1500, () => {
                explosion.destroy();
                debris.destroy();
                resolve();
            });
        });
    }

    /**
     * Play lightning animation
     */
    private async playLightningAnimation(x: number, y: number, gridX: number, gridY: number, gridSize: { rows: number, cols: number }): Promise<void> {
        return new Promise((resolve) => {
            // Lightning bolt từ top
            const lightning = this.scene.add.sprite(x, 0, 'particle');
            lightning.setScale(0.5, 10);
            lightning.setTint(0xffff44);
            lightning.setBlendMode(Phaser.BlendModes.ADD);
            lightning.setAlpha(0);

            this.scene.tweens.add({
                targets: lightning,
                alpha: { from: 0, to: 1 },
                duration: 300,
                ease: 'Power2.easeInOut',
                onComplete: () => {
                    lightning.destroy();
                }
            });

            // Horizontal lightning
            const horizontalLightning = this.scene.add.sprite(0, y, 'particle');
            horizontalLightning.setScale(20, 0.5);
            horizontalLightning.setTint(0xffff44);
            horizontalLightning.setBlendMode(Phaser.BlendModes.ADD);
            horizontalLightning.setAlpha(0);

            this.scene.tweens.add({
                targets: horizontalLightning,
                alpha: { from: 0, to: 1 },
                duration: 300,
                ease: 'Power2.easeInOut',
                onComplete: () => {
                    horizontalLightning.destroy();
                }
            });

            // Electric particles
            const electric = this.scene.add.particles(x, y, 'particle', {
                speed: { min: 200, max: 400 },
                scale: { start: 0.3, end: 0 },
                alpha: { start: 1, end: 0 },
                lifespan: 600,
                quantity: 25,
                angle: { min: 0, max: 360 },
                tint: [0xffff44, 0xffffff, 0x44ffff],
                blendMode: 'ADD'
            });

            // Screen flash
            this.createScreenFlash(0xffff44, 150);

            // Screen shake
            this.scene.cameras.main.shake(200, 0.015);

            this.scene.time.delayedCall(800, () => {
                electric.destroy();
                resolve();
            });
        });
    }

    /**
     * Play hammer animation
     */
    private async playHammerAnimation(x: number, y: number): Promise<void> {
        return new Promise((resolve) => {
            // Create hammer shadow first
            const shadow = this.scene.add.ellipse(x, y + 10, 40, 20, 0x000000, 0.3);
            shadow.setScale(0);

            // Hammer sprite
            const hammer = this.scene.add.sprite(x, y - 120, 'particle');
            hammer.setScale(2);
            hammer.setTint(0x666666);
            hammer.setRotation(-0.8);

            // Animate shadow growing
            this.scene.tweens.add({
                targets: shadow,
                scaleX: 1,
                scaleY: 0.5,
                duration: 200,
                ease: 'Power2.easeOut'
            });

            // Hammer smash animation với anticipation
            this.scene.tweens.add({
                targets: hammer,
                y: y - 140,
                rotation: -1,
                duration: 100,
                ease: 'Power1.easeOut',
                onComplete: () => {
                    // Fast smash down
                    this.scene.tweens.add({
                        targets: hammer,
                        y: y + 5,
                        rotation: 0.2,
                        scaleX: 2.2,
                        scaleY: 1.8,
                        duration: 150,
                        ease: 'Power3.easeIn',
                        onComplete: () => {
                            // Impact effects
                            this.createHammerImpactEffects(x, y);

                            // Hammer bounce back
                            this.scene.tweens.add({
                                targets: hammer,
                                y: y - 20,
                                scaleX: 2,
                                scaleY: 2,
                                duration: 100,
                                ease: 'Back.easeOut',
                                onComplete: () => {
                                    // Fade out hammer and shadow
                                    this.scene.tweens.add({
                                        targets: [hammer, shadow],
                                        alpha: 0,
                                        duration: 200,
                                        onComplete: () => {
                                            hammer.destroy();
                                            shadow.destroy();
                                            resolve();
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            });
        });
    }

    /**
     * Create hammer impact effects
     */
    private createHammerImpactEffects(x: number, y: number): void {
        // Main impact explosion
        const impact = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 150, max: 300 },
            scale: { start: 1, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 500,
            quantity: 20,
            angle: { min: 0, max: 360 },
            tint: [0xffffff, 0xffff88, 0x888888],
            blendMode: 'ADD'
        });

        // Debris particles
        const debris = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 80, max: 180 },
            scale: { start: 0.6, end: 0 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 800,
            quantity: 12,
            angle: { min: -45, max: 45 },
            tint: [0x666666, 0x444444, 0x888888],
            gravityY: 300
        });

        // Shockwave ring
        const shockwave = this.scene.add.sprite(x, y, 'particle');
        shockwave.setScale(0);
        shockwave.setTint(0xffffff);
        shockwave.setAlpha(0.8);
        shockwave.setBlendMode(Phaser.BlendModes.ADD);

        this.scene.tweens.add({
            targets: shockwave,
            scaleX: 3,
            scaleY: 3,
            alpha: 0,
            duration: 300,
            ease: 'Power2.easeOut',
            onComplete: () => {
                shockwave.destroy();
            }
        });

        // Screen shake với multiple waves
        this.scene.cameras.main.shake(200, 0.015);

        // Secondary shake
        this.scene.time.delayedCall(100, () => {
            this.scene.cameras.main.shake(100, 0.008);
        });

        // Cleanup particles
        this.scene.time.delayedCall(1000, () => {
            impact.destroy();
            debris.destroy();
        });
    }

    /**
     * Play shuffle animation
     */
    private async playShuffleAnimation(): Promise<void> {
        return new Promise((resolve) => {
            const centerX = this.scene.cameras.main.centerX;
            const centerY = this.scene.cameras.main.centerY;

            // Create multiple swirl layers
            this.createShuffleSwirls(centerX, centerY);

            // Create energy waves
            this.createShuffleEnergyWaves(centerX, centerY);

            // Screen effects
            this.createScreenFlash(0x44ff44, 400);

            // Screen shake with rotation
            this.scene.cameras.main.shake(500, 0.01);

            // Rotate camera slightly for disorientation effect
            this.scene.tweens.add({
                targets: this.scene.cameras.main,
                rotation: 0.1,
                duration: 250,
                yoyo: true,
                ease: 'Sine.easeInOut'
            });

            this.scene.time.delayedCall(1500, () => {
                resolve();
            });
        });
    }

    /**
     * Create shuffle swirl effects
     */
    private createShuffleSwirls(centerX: number, centerY: number): void {
        // Main swirl
        const mainSwirl = this.scene.add.particles(centerX, centerY, 'particle', {
            speed: { min: 200, max: 400 },
            scale: { start: 1.2, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 1200,
            quantity: 60,
            angle: { min: 0, max: 360 },
            tint: [0x44ff44, 0x88ff88, 0xaaffaa, 0x66ff66],
            blendMode: 'ADD',
            emitZone: {
                type: 'edge',
                source: new Phaser.Geom.Circle(0, 0, 50),
                quantity: 60
            }
        });

        // Counter-rotating swirl
        const counterSwirl = this.scene.add.particles(centerX, centerY, 'particle', {
            speed: { min: 150, max: 300 },
            scale: { start: 0.8, end: 0 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 1000,
            quantity: 40,
            angle: { min: 0, max: 360 },
            tint: [0x22aa22, 0x44cc44, 0x66ee66],
            blendMode: 'ADD',
            emitZone: {
                type: 'edge',
                source: new Phaser.Geom.Circle(0, 0, 80),
                quantity: 40
            }
        });

        // Cleanup
        this.scene.time.delayedCall(1500, () => {
            mainSwirl.destroy();
            counterSwirl.destroy();
        });
    }

    /**
     * Create shuffle energy waves
     */
    private createShuffleEnergyWaves(centerX: number, centerY: number): void {
        // Create expanding energy rings
        for (let i = 0; i < 3; i++) {
            this.scene.time.delayedCall(i * 200, () => {
                const energyRing = this.scene.add.sprite(centerX, centerY, 'particle');
                energyRing.setScale(0);
                energyRing.setTint(0x44ff44);
                energyRing.setAlpha(0.6);
                energyRing.setBlendMode(Phaser.BlendModes.ADD);

                this.scene.tweens.add({
                    targets: energyRing,
                    scaleX: 8,
                    scaleY: 8,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2.easeOut',
                    onComplete: () => {
                        energyRing.destroy();
                    }
                });
            });
        }

        // Create spiral energy beams
        for (let angle = 0; angle < 360; angle += 45) {
            const beam = this.scene.add.sprite(centerX, centerY, 'particle');
            beam.setScale(0.3, 4);
            beam.setTint(0x88ff88);
            beam.setAlpha(0.8);
            beam.setRotation(Phaser.Math.DegToRad(angle));
            beam.setBlendMode(Phaser.BlendModes.ADD);

            this.scene.tweens.add({
                targets: beam,
                rotation: beam.rotation + Phaser.Math.DegToRad(180),
                alpha: 0,
                duration: 1000,
                ease: 'Power2.easeOut',
                onComplete: () => {
                    beam.destroy();
                }
            });
        }
    }

    /**
     * Play color blast animation
     */
    private async playColorBlastAnimation(x: number, y: number, targetColor: number): Promise<void> {
        return new Promise((resolve) => {
            // Get appropriate color for the blast
            const blastColor = this.getColorBlastColor(targetColor);

            // Create initial pulse
            this.createColorBlastPulse(x, y, blastColor);

            // Create expanding color waves
            this.createColorBlastWaves(x, y, blastColor);

            // Create color-specific particles
            this.createColorBlastParticles(x, y, blastColor);

            // Screen effects
            this.createScreenFlash(blastColor, 300);

            // Color-tinted screen overlay
            this.createColorOverlay(blastColor);

            this.scene.time.delayedCall(1500, () => {
                resolve();
            });
        });
    }

    /**
     * Get color blast color based on target gem color
     */
    private getColorBlastColor(targetColor: number): number {
        // Map gem colors to more vibrant blast colors
        const colorMap: Record<number, number> = {
            0xff4444: 0xff0000, // red -> bright red
            0x4444ff: 0x0088ff, // blue -> bright blue
            0x44ff44: 0x00ff00, // green -> bright green
            0xffff44: 0xffff00, // yellow -> bright yellow
            0xff44ff: 0xff00ff, // purple -> bright purple
            0xff8844: 0xff6600, // orange -> bright orange
            0xffffff: 0xffffff  // white -> white
        };

        return colorMap[targetColor] || 0xff44ff;
    }

    /**
     * Create color blast pulse effect
     */
    private createColorBlastPulse(x: number, y: number, color: number): void {
        const pulse = this.scene.add.sprite(x, y, 'particle');
        pulse.setScale(0);
        pulse.setTint(color);
        pulse.setAlpha(0.9);
        pulse.setBlendMode(Phaser.BlendModes.ADD);

        this.scene.tweens.add({
            targets: pulse,
            scaleX: 4,
            scaleY: 4,
            alpha: 0,
            duration: 400,
            ease: 'Power3.easeOut',
            onComplete: () => {
                pulse.destroy();
            }
        });
    }

    /**
     * Create color blast waves
     */
    private createColorBlastWaves(x: number, y: number, color: number): void {
        // Create multiple expanding waves
        for (let i = 0; i < 4; i++) {
            this.scene.time.delayedCall(i * 150, () => {
                const wave = this.scene.add.sprite(x, y, 'particle');
                wave.setScale(0);
                wave.setTint(color);
                wave.setAlpha(0.6 - i * 0.1);
                wave.setBlendMode(Phaser.BlendModes.ADD);

                this.scene.tweens.add({
                    targets: wave,
                    scaleX: 12 + i * 2,
                    scaleY: 12 + i * 2,
                    alpha: 0,
                    duration: 800 + i * 100,
                    ease: 'Power2.easeOut',
                    onComplete: () => {
                        wave.destroy();
                    }
                });
            });
        }
    }

    /**
     * Create color blast particles
     */
    private createColorBlastParticles(x: number, y: number, color: number): void {
        // Main color explosion
        const explosion = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 200, max: 500 },
            scale: { start: 1.2, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 1200,
            quantity: 50,
            angle: { min: 0, max: 360 },
            tint: color,
            blendMode: 'ADD'
        });

        // Trailing sparkles
        const sparkles = this.scene.add.particles(x, y, 'particle', {
            speed: { min: 100, max: 300 },
            scale: { start: 0.6, end: 0 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 1500,
            quantity: 30,
            angle: { min: 0, max: 360 },
            tint: [color, 0xffffff],
            blendMode: 'ADD',
            gravityY: -50
        });

        // Cleanup
        this.scene.time.delayedCall(1800, () => {
            explosion.destroy();
            sparkles.destroy();
        });
    }

    /**
     * Create color overlay effect
     */
    private createColorOverlay(color: number): void {
        const overlay = this.scene.add.rectangle(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            this.scene.cameras.main.width,
            this.scene.cameras.main.height,
            color,
            0.2
        );

        overlay.setDepth(1500);
        overlay.setBlendMode(Phaser.BlendModes.ADD);

        this.scene.tweens.add({
            targets: overlay,
            alpha: 0,
            duration: 600,
            ease: 'Power2.easeOut',
            onComplete: () => {
                overlay.destroy();
            }
        });
    }

    /**
     * Create screen flash effect
     */
    private createScreenFlash(color: number, duration: number): void {
        const flash = this.scene.add.rectangle(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            this.scene.cameras.main.width,
            this.scene.cameras.main.height,
            color,
            0.3
        );
        
        flash.setDepth(2000);
        
        this.scene.tweens.add({
            targets: flash,
            alpha: 0,
            duration: duration,
            ease: 'Power2.easeOut',
            onComplete: () => {
                flash.destroy();
            }
        });
    }

    /**
     * Find possible moves on the board
     */
    private findPossibleMoves(getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): Array<{ gem1: Gem, gem2: Gem }> {
        const possibleMoves: Array<{ gem1: Gem, gem2: Gem }> = [];

        for (let row = 0; row < gridSize.rows; row++) {
            for (let col = 0; col < gridSize.cols; col++) {
                const gem1 = getGemAt(col, row);
                if (!gem1) continue;

                // Check adjacent positions
                const directions = [
                    { dr: 0, dc: 1 },  // right
                    { dr: 1, dc: 0 },  // down
                ];

                for (const dir of directions) {
                    const newRow = row + dir.dr;
                    const newCol = col + dir.dc;

                    if (newRow < gridSize.rows && newCol < gridSize.cols) {
                        const gem2 = getGemAt(newCol, newRow);
                        if (gem2 && this.wouldCreateMatchAfterSwap(gem1, gem2, getGemAt, gridSize)) {
                            possibleMoves.push({ gem1, gem2 });
                        }
                    }
                }
            }
        }

        return possibleMoves;
    }

    /**
     * Check if swapping two gems would create a match
     */
    private wouldCreateMatchAfterSwap(gem1: Gem, gem2: Gem, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): boolean {
        // Temporarily swap gem types
        const originalType1 = gem1.gemType;
        const originalType2 = gem2.gemType;

        gem1.gemType = originalType2;
        gem2.gemType = originalType1;

        // Check for matches at both positions
        const hasMatch = this.checkForMatchAtPosition(gem1.gridX, gem1.gridY, getGemAt, gridSize) ||
                        this.checkForMatchAtPosition(gem2.gridX, gem2.gridY, getGemAt, gridSize);

        // Restore original types
        gem1.gemType = originalType1;
        gem2.gemType = originalType2;

        return hasMatch;
    }

    /**
     * Check for match at specific position
     */
    private checkForMatchAtPosition(col: number, row: number, getGemAt: (gridX: number, gridY: number) => Gem | null, gridSize: { rows: number, cols: number }): boolean {
        const gem = getGemAt(col, row);
        if (!gem) return false;

        const gemType = gem.gemType;

        // Check horizontal
        let horizontalCount = 1;

        // Check left
        for (let c = col - 1; c >= 0; c--) {
            const leftGem = getGemAt(c, row);
            if (leftGem && leftGem.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }

        // Check right
        for (let c = col + 1; c < gridSize.cols; c++) {
            const rightGem = getGemAt(c, row);
            if (rightGem && rightGem.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }

        if (horizontalCount >= 3) return true;

        // Check vertical
        let verticalCount = 1;

        // Check up
        for (let r = row - 1; r >= 0; r--) {
            const upGem = getGemAt(col, r);
            if (upGem && upGem.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }

        // Check down
        for (let r = row + 1; r < gridSize.rows; r++) {
            const downGem = getGemAt(col, r);
            if (downGem && downGem.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }

        return verticalCount >= 3;
    }

    /**
     * Play hint animation for possible moves
     */
    private async playHintAnimation(possibleMoves: Array<{ gem1: Gem, gem2: Gem }>): Promise<void> {
        return new Promise((resolve) => {
            // Show hint for only one move to avoid clutter
            const movesToShow = possibleMoves.slice(0, 1);

            movesToShow.forEach(({ gem1, gem2 }, index) => {
                const delay = index * 200; // Stagger hints

                this.scene.time.delayedCall(delay, () => {
                    // Enhanced hint animation
                    [gem1, gem2].forEach(gem => {
                        // Glow effect
                        const glow = this.scene.add.sprite(gem.x, gem.y, 'particle');
                        glow.setScale(1.5);
                        glow.setTint(0x44ffff);
                        glow.setAlpha(0.6);
                        glow.setBlendMode(Phaser.BlendModes.ADD);
                        glow.setDepth(gem.depth + 1);

                        // Pulsing animation
                        this.scene.tweens.add({
                            targets: [gem, glow],
                            scaleX: 1.2,
                            scaleY: 1.2,
                            alpha: { from: 1, to: 0.6 },
                            duration: 400,
                            yoyo: true,
                            repeat: 3,
                            ease: 'Sine.easeInOut',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });

                        // Sparkle particles
                        const sparkles = this.scene.add.particles(gem.x, gem.y, 'particle', {
                            speed: { min: 30, max: 80 },
                            scale: { start: 0.3, end: 0 },
                            alpha: { start: 0.8, end: 0 },
                            lifespan: 800,
                            quantity: 8,
                            angle: { min: 0, max: 360 },
                            tint: 0x44ffff,
                            blendMode: 'ADD'
                        });

                        this.scene.time.delayedCall(1600, () => {
                            sparkles.destroy();
                        });
                    });
                });
            });

            // Show hint message
            this.showHintMessage(movesToShow.length);

            // Resolve after all animations
            this.scene.time.delayedCall(2000, () => {
                resolve();
            });
        });
    }

    /**
     * Show hint message
     */
    private showHintMessage(moveCount: number): void {
        const centerX = this.scene.cameras.main.centerX;
        const centerY = this.scene.cameras.main.centerY;

        const message = moveCount > 1 ? `${moveCount} possible moves found!` : 'Possible move highlighted!';

        const hintText = this.scene.add.text(centerX, centerY - 150, message, {
            fontSize: '24px',
            color: '#44ffff',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 3
        });
        hintText.setOrigin(0.5);
        hintText.setDepth(2000);

        // Animation
        hintText.setAlpha(0);
        this.scene.tweens.add({
            targets: hintText,
            alpha: 1,
            y: hintText.y - 20,
            duration: 300,
            ease: 'Back.easeOut',
            onComplete: () => {
                this.scene.time.delayedCall(1500, () => {
                    this.scene.tweens.add({
                        targets: hintText,
                        alpha: 0,
                        y: hintText.y - 30,
                        duration: 500,
                        onComplete: () => {
                            hintText.destroy();
                        }
                    });
                });
            }
        });
    }

    /**
     * Play no moves animation
     */
    private async playNoMovesAnimation(): Promise<void> {
        return new Promise((resolve) => {
            const centerX = this.scene.cameras.main.centerX;
            const centerY = this.scene.cameras.main.centerY;

            // No moves message
            const noMovesText = this.scene.add.text(centerX, centerY, 'No moves available!\nTry using Shuffle power-up', {
                fontSize: '28px',
                color: '#ff4444',
                fontStyle: 'bold',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            });
            noMovesText.setOrigin(0.5);
            noMovesText.setDepth(2000);

            // Warning particles
            const warningParticles = this.scene.add.particles(centerX, centerY, 'particle', {
                speed: { min: 50, max: 150 },
                scale: { start: 0.8, end: 0 },
                alpha: { start: 1, end: 0 },
                lifespan: 1000,
                quantity: 20,
                angle: { min: 0, max: 360 },
                tint: [0xff4444, 0xff8844],
                blendMode: 'ADD'
            });

            // Animation
            noMovesText.setScale(0);
            this.scene.tweens.add({
                targets: noMovesText,
                scaleX: 1,
                scaleY: 1,
                duration: 400,
                ease: 'Back.easeOut',
                onComplete: () => {
                    this.scene.time.delayedCall(2000, () => {
                        this.scene.tweens.add({
                            targets: noMovesText,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 500,
                            onComplete: () => {
                                noMovesText.destroy();
                                warningParticles.destroy();
                                resolve();
                            }
                        });
                    });
                }
            });
        });
    }
}
