import { GemType, GemTypeUtils } from '../types/GemTypes';
import { GemUnlockConfig } from '../managers/GemUnlockManager';

/**
 * Interface cho gem unlock notification data
 */
export interface GemUnlockData {
    levelId: number;
    newGems: GemType[];
    configs: (GemUnlockConfig | null)[];
}

/**
 * UI component để hiển thị notification khi unlock gems mới
 */
export class GemUnlockNotification {
    private scene: Phaser.Scene;
    private container?: Phaser.GameObjects.Container;
    private isVisible: boolean = false;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('gems-unlocked', this.onGemsUnlocked, this);
    }

    /**
     * Handle gems unlocked event
     */
    private onGemsUnlocked(data: GemUnlockData): void {
        if (data.newGems.length > 0) {
            this.showNotification(data);
        }
    }

    /**
     * Show gem unlock notification
     */
    public showNotification(data: GemUnlockData): void {
        if (this.isVisible) return;

        this.isVisible = true;
        this.createNotification(data);
    }

    /**
     * Create notification UI
     */
    private createNotification(data: GemUnlockData): void {
        // Create container
        this.container = this.scene.add.container(512, 384);
        this.container.setDepth(1000);

        // Create background
        const background = this.scene.add.rectangle(0, 0, 600, 400, 0x000000, 0.8);
        background.setStrokeStyle(4, 0xffd700);
        this.container.add(background);

        // Create title
        const title = this.scene.add.text(0, -150, 'NEW GEMS UNLOCKED!', {
            fontSize: '32px',
            color: '#ffd700',
            fontStyle: 'bold'
        });
        title.setOrigin(0.5);
        this.container.add(title);

        // Create subtitle
        const subtitle = this.scene.add.text(0, -110, `Level ${data.levelId} Completed`, {
            fontSize: '18px',
            color: '#ffffff'
        });
        subtitle.setOrigin(0.5);
        this.container.add(subtitle);

        // Create gem previews
        this.createGemPreviews(data);

        // Create continue button
        this.createContinueButton();

        // Add entrance animation
        this.animateEntrance();
    }

    /**
     * Create gem preview displays
     */
    private createGemPreviews(data: GemUnlockData): void {
        if (!this.container) return;

        const startY = -20;
        const gemSpacing = 120;
        const maxGemsPerRow = 4;
        
        data.newGems.forEach((gemType, index) => {
            const config = data.configs[index];
            if (!config) return;

            const row = Math.floor(index / maxGemsPerRow);
            const col = index % maxGemsPerRow;
            const totalInRow = Math.min(data.newGems.length - (row * maxGemsPerRow), maxGemsPerRow);
            
            const x = (col - (totalInRow - 1) / 2) * gemSpacing;
            const y = startY + (row * 100);

            // Create gem preview container
            const gemContainer = this.scene.add.container(x, y);
            this.container!.add(gemContainer);

            // Create gem sprite
            const gemTexture = GemTypeUtils.getGemTexture(gemType);
            const gemSprite = this.scene.add.sprite(0, -10, 'game_atlas', gemTexture);
            gemSprite.setScale(0.8);
            gemContainer.add(gemSprite);

            // Create gem name
            const gemName = this.scene.add.text(0, 35, config.name, {
                fontSize: '16px',
                color: '#ffd700',
                fontStyle: 'bold'
            });
            gemName.setOrigin(0.5);
            gemContainer.add(gemName);

            // Create gem description
            const description = this.scene.add.text(0, 55, this.wrapText(config.description, 15), {
                fontSize: '12px',
                color: '#cccccc',
                align: 'center'
            });
            description.setOrigin(0.5);
            gemContainer.add(description);

            // Add sparkle effect
            this.addSparkleEffect(gemContainer, gemSprite);

            // Staggered entrance animation
            gemContainer.setAlpha(0);
            gemContainer.setScale(0);
            this.scene.tweens.add({
                targets: gemContainer,
                alpha: 1,
                scale: 1,
                duration: 500,
                delay: index * 200,
                ease: 'Back.easeOut'
            });
        });
    }

    /**
     * Add sparkle effect to gem
     */
    private addSparkleEffect(container: Phaser.GameObjects.Container, gemSprite: Phaser.GameObjects.Sprite): void {
        // Create sparkle particles around gem
        const sparkles: Phaser.GameObjects.Sprite[] = [];
        
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const radius = 40;
            const x = Math.cos(angle) * radius;
            const y = Math.sin(angle) * radius;
            
            const sparkle = this.scene.add.sprite(x, y, 'game_atlas', 'star');
            sparkle.setScale(0.3);
            sparkle.setAlpha(0.8);
            sparkle.setTint(0xffd700);
            container.add(sparkle);
            sparkles.push(sparkle);
            
            // Animate sparkles
            this.scene.tweens.add({
                targets: sparkle,
                rotation: Math.PI * 2,
                alpha: { from: 0.8, to: 0.3 },
                scale: { from: 0.3, to: 0.1 },
                duration: 2000,
                repeat: -1,
                delay: i * 100
            });
        }

        // Gem glow effect
        this.scene.tweens.add({
            targets: gemSprite,
            scale: { from: 0.8, to: 0.9 },
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    /**
     * Create continue button
     */
    private createContinueButton(): void {
        if (!this.container) return;

        const buttonY = 150;
        
        // Button background
        const buttonBg = this.scene.add.rectangle(0, buttonY, 200, 50, 0x4CAF50);
        buttonBg.setStrokeStyle(2, 0xffffff);
        buttonBg.setInteractive();
        this.container.add(buttonBg);

        // Button text
        const buttonText = this.scene.add.text(0, buttonY, 'CONTINUE', {
            fontSize: '18px',
            color: '#ffffff',
            fontStyle: 'bold'
        });
        buttonText.setOrigin(0.5);
        this.container.add(buttonText);

        // Button hover effects
        buttonBg.on('pointerover', () => {
            buttonBg.setFillStyle(0x66BB6A);
            this.scene.tweens.add({
                targets: buttonBg,
                scaleX: 1.05,
                scaleY: 1.05,
                duration: 100
            });
        });

        buttonBg.on('pointerout', () => {
            buttonBg.setFillStyle(0x4CAF50);
            this.scene.tweens.add({
                targets: buttonBg,
                scaleX: 1,
                scaleY: 1,
                duration: 100
            });
        });

        // Button click
        buttonBg.on('pointerdown', () => {
            this.hideNotification();
        });
    }

    /**
     * Animate entrance
     */
    private animateEntrance(): void {
        if (!this.container) return;

        this.container.setAlpha(0);
        this.container.setScale(0.8);

        this.scene.tweens.add({
            targets: this.container,
            alpha: 1,
            scale: 1,
            duration: 600,
            ease: 'Back.easeOut'
        });
    }

    /**
     * Hide notification
     */
    private hideNotification(): void {
        if (!this.container || !this.isVisible) return;

        this.scene.tweens.add({
            targets: this.container,
            alpha: 0,
            scale: 0.8,
            duration: 400,
            ease: 'Power2.easeIn',
            onComplete: () => {
                this.container?.destroy();
                this.container = undefined;
                this.isVisible = false;
            }
        });
    }

    /**
     * Wrap text to fit within specified character limit
     */
    private wrapText(text: string, maxCharsPerLine: number): string {
        const words = text.split(' ');
        const lines: string[] = [];
        let currentLine = '';

        words.forEach(word => {
            if ((currentLine + word).length <= maxCharsPerLine) {
                currentLine += (currentLine ? ' ' : '') + word;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                }
                currentLine = word;
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines.join('\n');
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('gems-unlocked', this.onGemsUnlocked, this);
        if (this.container) {
            this.container.destroy();
        }
    }
}
