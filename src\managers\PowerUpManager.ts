import { PowerUpType, PowerUpInventoryItem, PowerUpActivationData, PowerUpUtils } from '../types/PowerUpTypes';

/**
 * Manager class cho power-up system
 */
export class PowerUpManager {
    private scene: Phaser.Scene;
    private inventory: Map<PowerUpType, PowerUpInventoryItem>;
    private isActivating: boolean = false;
    private isProcessing: boolean = false;
    private selectedPowerUp: PowerUpType | null = null;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.inventory = new Map();
        this.initializeInventory();
        this.setupEventListeners();

        // Emit initial inventory update after a frame to ensure UI is ready
        this.scene.time.delayedCall(1, () => {
            this.scene.events.emit('power-up-inventory-updated', this.getInventoryData());
        });
    }

    /**
     * Initialize inventory với starting power-ups
     */
    private initializeInventory(): void {
        // Give player some starting power-ups for testing
        this.inventory.set(PowerUpType.BOMB, {
            type: PowerUpType.BOMB,
            quantity: 3,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });

        this.inventory.set(PowerUpType.LIGHTNING, {
            type: PowerUpType.LIGHTNING,
            quantity: 2,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });

        this.inventory.set(PowerUpType.HAMMER, {
            type: PowerUpType.HAMMER,
            quantity: 5,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });

        this.inventory.set(PowerUpType.SHUFFLE, {
            type: PowerUpType.SHUFFLE,
            quantity: 1,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });

        this.inventory.set(PowerUpType.COLOR_BLAST, {
            type: PowerUpType.COLOR_BLAST,
            quantity: 2,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });

        this.inventory.set(PowerUpType.HINT, {
            type: PowerUpType.HINT,
            quantity: 5,
            isOnCooldown: false,
            cooldownMovesRemaining: 0
        });
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('power-up-selected', this.onPowerUpSelected, this);
        this.scene.events.on('power-up-activate', this.activatePowerUp, this);
        this.scene.events.on('board-clicked', this.onBoardClicked, this);
        this.scene.events.on('power-up-processing-started', this.onProcessingStarted, this);
        this.scene.events.on('power-up-processing-finished', this.onProcessingFinished, this);
        this.scene.events.on('move-made', this.onMoveMade, this); // New: listen for moves
    }

    /**
     * Handle power-up selection từ UI
     */
    private onPowerUpSelected(powerUpType: PowerUpType): void {
        // Prevent selection if currently processing another power-up
        if (this.isProcessing) {
            console.log('Cannot select power-up while processing');
            return;
        }

        if (!this.canUsePowerUp(powerUpType)) {
            console.log(`Cannot use power-up: ${powerUpType}`);
            return;
        }

        this.selectedPowerUp = powerUpType;
        this.scene.events.emit('power-up-mode-activated', powerUpType);

        // Change cursor hoặc visual indicator
        this.scene.input.setDefaultCursor('crosshair');
    }

    /**
     * Handle board click khi power-up được selected
     */
    private onBoardClicked(data: { gridX: number; gridY: number; worldX: number; worldY: number }): void {
        if (!this.selectedPowerUp || this.isActivating) return;

        // Validate target position trước khi activate
        if (!this.isValidPowerUpTarget(this.selectedPowerUp, data.gridX, data.gridY)) {
            // Show invalid target feedback
            this.showInvalidTargetFeedback(data.worldX, data.worldY);
            return;
        }

        const activationData: PowerUpActivationData = {
            type: this.selectedPowerUp,
            position: {
                x: data.worldX,
                y: data.worldY,
                gridX: data.gridX,
                gridY: data.gridY
            }
        };

        this.activatePowerUp(activationData);
    }

    /**
     * Activate power-up
     */
    public async activatePowerUp(data: PowerUpActivationData): Promise<void> {
        if (this.isActivating || !this.canUsePowerUp(data.type)) return;

        this.isActivating = true;

        try {
            // Emit activation event cho GameBoard và đợi kết quả
            const effectResult = await this.executeAndValidatePowerUp(data);

            if (effectResult.success) {
                // Chỉ tiêu tốn power-up khi effect thành công
                this.usePowerUp(data.type);

                // Reset selection
                this.selectedPowerUp = null;
                this.scene.input.setDefaultCursor('default');

                // Update UI
                this.scene.events.emit('power-up-inventory-updated', this.getInventoryData());
            } else {
                // Effect không thành công, show feedback
                this.showInvalidTargetFeedback(data.position?.x || 0, data.position?.y || 0);
            }

        } catch (error) {
            console.error('Error activating power-up:', error);
        } finally {
            this.isActivating = false;
        }
    }

    /**
     * Check if power-up có thể sử dụng
     */
    public canUsePowerUp(type: PowerUpType): boolean {
        const item = this.inventory.get(type);
        if (!item) return false;

        return PowerUpUtils.isAvailable(item);
    }

    /**
     * Validate if target position is valid cho power-up
     */
    private isValidPowerUpTarget(powerUpType: PowerUpType, gridX: number, gridY: number): boolean {
        // Get GameBoard reference để kiểm tra cell
        const gameBoard = (this.scene as any).gameBoard;
        if (!gameBoard) return false;

        // Kiểm tra bounds
        if (gridX < 0 || gridY < 0 || gridX >= gameBoard.getBoardWidth() || gridY >= gameBoard.getBoardHeight()) {
            return false;
        }

        const cell = gameBoard.getGrid()[gridY] && gameBoard.getGrid()[gridY][gridX] ? gameBoard.getGrid()[gridY][gridX] : null;

        switch (powerUpType) {
            case PowerUpType.COLOR_BLAST:
                // Color Blast cần gem hợp lệ, không phải obstacle
                return cell && cell.constructor.name === 'Gem';

            case PowerUpType.HAMMER:
                // Hammer có thể phá gem hoặc destructible obstacle
                if (cell && cell.constructor.name === 'Gem') return true;
                if (cell && cell.constructor.name === 'Obstacle' && cell.properties?.isDestructible) return true;
                return false;

            case PowerUpType.BOMB:
            case PowerUpType.LIGHTNING:
            case PowerUpType.SHUFFLE:
                // Các power-up khác có thể dùng ở bất kỳ đâu trên board
                return true;

            case PowerUpType.HINT:
                // Hint không cần target cụ thể
                return true;

            default:
                return false;
        }
    }

    /**
     * Execute power-up và validate kết quả
     */
    private async executeAndValidatePowerUp(data: PowerUpActivationData): Promise<{ success: boolean; gemsDestroyed: number }> {
        return new Promise((resolve) => {
            // Listen for power-up result
            const onPowerUpResult = (result: { success: boolean; gemsDestroyed: number }) => {
                this.scene.events.off('power-up-result', onPowerUpResult);
                resolve(result);
            };

            this.scene.events.on('power-up-result', onPowerUpResult);

            // Emit activation event
            this.scene.events.emit('power-up-activated', data);

            // Timeout fallback
            this.scene.time.delayedCall(3000, () => {
                this.scene.events.off('power-up-result', onPowerUpResult);
                resolve({ success: false, gemsDestroyed: 0 });
            });
        });
    }

    /**
     * Show feedback khi target không hợp lệ
     */
    private showInvalidTargetFeedback(x: number, y: number): void {
        // Create "X" mark tại vị trí click
        const invalidMark = this.scene.add.text(x, y, '✗', {
            fontSize: '32px',
            color: '#ff4444',
            fontStyle: 'bold'
        });
        invalidMark.setOrigin(0.5);
        invalidMark.setDepth(1000);

        // Animate invalid mark
        this.scene.tweens.add({
            targets: invalidMark,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 800,
            ease: 'Power2.easeOut',
            onComplete: () => {
                invalidMark.destroy();
            }
        });

        // Play error sound (if available)
        if (this.scene.sound.get('error')) {
            this.scene.sound.play('error', { volume: 0.3 });
        }

        // Show tooltip message
        this.showInvalidTargetTooltip(x, y);
    }

    /**
     * Show tooltip cho invalid target
     */
    private showInvalidTargetTooltip(x: number, y: number): void {
        const tooltip = this.scene.add.text(x, y - 50, 'Invalid Target!', {
            fontSize: '16px',
            color: '#ffffff',
            backgroundColor: '#ff4444',
            padding: { x: 8, y: 4 }
        });
        tooltip.setOrigin(0.5);
        tooltip.setDepth(1001);

        // Animate tooltip
        this.scene.tweens.add({
            targets: tooltip,
            y: y - 80,
            alpha: 0,
            duration: 1500,
            ease: 'Power2.easeOut',
            onComplete: () => {
                tooltip.destroy();
            }
        });
    }

    /**
     * Use power-up từ inventory
     */
    private usePowerUp(type: PowerUpType): void {
        const item = this.inventory.get(type);
        if (!item || item.quantity <= 0) return;

        // Decrease quantity
        item.quantity--;

        // Set move-based cooldown
        const moveCooldown = PowerUpUtils.getMoveCooldown(type);
        if (moveCooldown > 0) {
            item.isOnCooldown = true;
            item.cooldownMovesRemaining = moveCooldown;
        } else {
            // No cooldown for this power-up (e.g., hints)
            item.isOnCooldown = false;
            item.cooldownMovesRemaining = 0;
        }

        // Legacy time-based cooldown (kept for compatibility)
        item.lastUsed = Date.now();
    }

    /**
     * Handle move made - decrement cooldowns
     */
    private onMoveMade(): void {
        let cooldownsUpdated = false;

        this.inventory.forEach(item => {
            if (item.isOnCooldown && item.cooldownMovesRemaining !== undefined && item.cooldownMovesRemaining > 0) {
                item.cooldownMovesRemaining--;

                // Remove cooldown when reaches 0
                if (item.cooldownMovesRemaining <= 0) {
                    item.isOnCooldown = false;
                    item.cooldownMovesRemaining = 0;
                }

                cooldownsUpdated = true;
            }
        });

        // Update UI if any cooldowns changed
        if (cooldownsUpdated) {
            this.scene.events.emit('power-up-inventory-updated', this.getInventoryData());
        }
    }

    /**
     * Add power-up to inventory
     */
    public addPowerUp(type: PowerUpType, quantity: number = 1): void {
        const item = this.inventory.get(type);
        if (item) {
            item.quantity += quantity;
        } else {
            this.inventory.set(type, {
                type,
                quantity,
                isOnCooldown: false,
                cooldownMovesRemaining: 0
            });
        }

        this.scene.events.emit('power-up-inventory-updated', this.getInventoryData());
    }

    /**
     * Get inventory data cho UI
     */
    public getInventoryData(): PowerUpInventoryItem[] {
        return Array.from(this.inventory.values());
    }

    /**
     * Get selected power-up
     */
    public getSelectedPowerUp(): PowerUpType | null {
        return this.selectedPowerUp;
    }

    /**
     * Cancel current power-up selection
     */
    public cancelSelection(): void {
        this.selectedPowerUp = null;
        this.scene.input.setDefaultCursor('default');
        this.scene.events.emit('power-up-mode-deactivated');
    }

    /**
     * Handle processing started
     */
    private onProcessingStarted(): void {
        this.isProcessing = true;
    }

    /**
     * Handle processing finished
     */
    private onProcessingFinished(): void {
        this.isProcessing = false;
    }

    /**
     * Reset power-ups for new level
     */
    public resetPowerUps(): void {
        // Cancel any active selection
        this.cancelSelection();

        // Reset activation and processing states
        this.isActivating = false;
        this.isProcessing = false;

        // Reset inventory to starting amounts
        this.initializeInventory();

        // Update UI
        this.scene.events.emit('power-up-inventory-updated', this.getInventoryData());
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('power-up-selected', this.onPowerUpSelected, this);
        this.scene.events.off('power-up-activate', this.activatePowerUp, this);
        this.scene.events.off('board-clicked', this.onBoardClicked, this);
        this.scene.events.off('power-up-processing-started', this.onProcessingStarted, this);
        this.scene.events.off('power-up-processing-finished', this.onProcessingFinished, this);

        this.inventory.clear();
    }
}
