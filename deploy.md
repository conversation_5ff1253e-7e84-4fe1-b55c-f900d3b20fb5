# Hướng dẫn Deploy Match 3 Game lên Cloudflare Pages

## Tổng quan

Match 3 Game được xây dựng với Phaser 3, Vite và Bun. Có 2 cách ch<PERSON>h để deploy lên Cloudflare Pages:

1. **Git Integration** - Tự động deploy từ GitHub/GitLab repository
2. **Direct Upload** - Upload trực tiếp build output bằng Wrangler CLI

## Yêu cầu

- [Bun](https://bun.sh) đã được cài đặt
- Tài kho<PERSON>n [Cloudflare](https://cloudflare.com) (miễn phí)
- [Wrangler CLI](https://developers.cloudflare.com/workers/wrangler/install-and-update/) cho Direct Upload

## Phương pháp 1: Git Integration (Khuyến nghị)

### Bước 1: Chuẩn bị Repository

1. Push code lên GitHub/GitLab repository:
```bash
git add .
git commit -m "Prepare for Cloudflare Pages deployment"
git push origin main
```

### Bước 2: <PERSON><PERSON><PERSON> nối với Cloudflare Pages

1. <PERSON><PERSON><PERSON> nhập vào [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Chọn **Pages** từ sidebar
3. Click **Create a project** > **Connect to Git**
4. Chọn repository của bạn
5. Cấu hình build settings:

**Build Configuration:**
- **Framework preset**: None (hoặc Vite)
- **Build command**: `bun run build`
- **Build output directory**: `dist`
- **Root directory**: `/` (để trống)

### Bước 3: Environment Variables (nếu cần)

Nếu game cần environment variables:
- Vào **Settings** > **Environment variables**
- Thêm các biến cần thiết

### Bước 4: Deploy

- Click **Save and Deploy**
- Cloudflare sẽ tự động build và deploy
- Domain sẽ có dạng: `your-project-name.pages.dev`

## Phương pháp 2: Direct Upload với Wrangler CLI

### Bước 1: Cài đặt Wrangler

```bash
# Cài đặt global
bun add -g wrangler

# Hoặc sử dụng npx
npx wrangler --version
```

### Bước 2: Đăng nhập Cloudflare

```bash
wrangler login
```

### Bước 3: Build Project

```bash
# Build production
bun run build

# Kiểm tra thư mục dist
ls -la dist/
```

### Bước 4: Deploy trực tiếp

```bash
# Deploy lần đầu (tạo project mới)
wrangler pages deploy dist --project-name=match3-game

# Deploy update (project đã tồn tại)
wrangler pages deploy dist
```

### Bước 5: Cấu hình tùy chọn với wrangler.toml

Tạo file `wrangler.toml` trong root directory:

```toml
name = "match3-game"
compatibility_date = "2025-01-15"

[assets]
directory = "./dist"
not_found_handling = "single-page-application"
html_handling = "auto-trailing-slash"
```

Sau đó deploy với:
```bash
wrangler deploy
```

## Cấu hình nâng cao

### Custom Domain

1. Vào **Pages** > **Custom domains**
2. Click **Set up a custom domain**
3. Nhập domain của bạn
4. Cấu hình DNS records theo hướng dẫn

### Headers và Redirects

Tạo file `public/_headers` cho custom headers:
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: no-referrer
```

Tạo file `public/_redirects` cho redirects:
```
/old-path/* /new-path/:splat 301
```

### Environment Variables cho Production

Trong Cloudflare Dashboard:
1. **Pages** > **Settings** > **Environment variables**
2. Thêm variables cho **Production** environment

## Scripts hữu ích

Thêm vào `package.json`:

```json
{
  "scripts": {
    "deploy": "bun run build && wrangler pages deploy dist",
    "deploy:preview": "bun run build && wrangler pages deploy dist --env preview",
    "pages:dev": "wrangler pages dev dist --port 8080"
  }
}
```

## Troubleshooting

### Lỗi Build

1. **Kiểm tra build command**:
```bash
bun run build
```

2. **Kiểm tra output directory**:
```bash
ls -la dist/
```

### Lỗi Assets không load

1. **Kiểm tra base path** trong `vite/config.prod.mjs`:
```javascript
export default defineConfig({
    base: './', // Đảm bảo relative paths
    // ...
});
```

2. **Kiểm tra assets trong dist**:
```bash
ls -la dist/assets/
```

### Lỗi 404 cho routes

Thêm vào `wrangler.toml`:
```toml
[assets]
not_found_handling = "single-page-application"
```

## Monitoring và Analytics

1. **Pages Analytics**: Tự động có sẵn trong Cloudflare Dashboard
2. **Web Analytics**: Enable trong **Analytics** > **Web Analytics**
3. **Real User Monitoring**: Có thể enable cho performance insights

## Kết luận

- **Git Integration** phù hợp cho development workflow liên tục
- **Direct Upload** phù hợp cho testing nhanh hoặc CI/CD custom
- Cloudflare Pages cung cấp global CDN, SSL miễn phí và performance tốt
- Domain mặc định: `your-project-name.pages.dev`

Sau khi deploy thành công, game sẽ có sẵn trên toàn cầu với hiệu suất cao nhờ Cloudflare's edge network.
