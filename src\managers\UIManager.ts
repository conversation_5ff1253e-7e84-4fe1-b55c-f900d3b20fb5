import { StarMeter } from '../ui/components/StarMeter';
import { GameMenuModal } from '../ui/GameMenuModal';

/**
 * Class quản lý UI elements cho game
 */
export class UIManager {
    private scene: Phaser.Scene;
    private scoreText?: Phaser.GameObjects.Text;
    private levelText?: Phaser.GameObjects.Text;
    private movesText?: Phaser.GameObjects.Text;
    private comboText?: Phaser.GameObjects.Text;
    private highScoreText?: Phaser.GameObjects.Text;

    // Topbar UI
    private topbarContainer?: Phaser.GameObjects.Container;
    private movesSection?: Phaser.GameObjects.Container;
    private objectivesSection?: Phaser.GameObjects.Container;
    private scoreSection?: Phaser.GameObjects.Container;
    private levelSection?: Phaser.GameObjects.Container;
    private objectiveTexts: Phaser.GameObjects.Text[] = [];

    // Legacy containers (kept for compatibility)
    private objectivesContainer?: Phaser.GameObjects.Container;
    private movesContainer?: Phaser.GameObjects.Container;

    // UI Container
    private uiContainer?: Phaser.GameObjects.Container;

    // Star Meter
    private starMeter?: StarMeter;
    private starMeterContainer?: Phaser.GameObjects.Container;
    private lastStarsCount: number = 0;

    // Menu Button & Modal
    private menuButton?: Phaser.GameObjects.Container;
    private gameMenuModal?: GameMenuModal;

    // Board positioning info for dynamic UI layout
    private boardCenterX: number = 512;
    private boardCenterY: number = 384; // Keep UI elements at original position
    private boardWidth: number = 480; // Default board area width
    private boardHeight: number = 480; // Default board area height
    
    // Style constants
    private readonly TEXT_STYLE = {
        fontSize: '24px',
        fontFamily: 'Arial, sans-serif',
        color: '#ffffff',
        stroke: '#000000',
        strokeThickness: 2
    };
    
    private readonly TITLE_STYLE = {
        fontSize: '18px',
        fontFamily: 'Arial, sans-serif',
        color: '#ffff00',
        stroke: '#000000',
        strokeThickness: 2
    };
    
    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.createUI();
        this.setupEventListeners();
    }

    /**
     * Update board positioning info for dynamic UI layout
     */
    public updateBoardPosition(centerX: number, centerY: number, width: number, height: number): void {
        this.boardCenterX = centerX;
        this.boardCenterY = centerY;
        this.boardWidth = width;
        this.boardHeight = height;

        // Reposition UI elements based on new board position
        this.repositionUIElements();
    }

    /**
     * Reposition UI elements based on current board position
     */
    private repositionUIElements(): void {
        // Reposition topbar container
        if (this.topbarContainer) {
            const topbarY = this.boardCenterY - (this.boardHeight / 2) - 80; // Above board
            this.topbarContainer.setPosition(this.boardCenterX, topbarY);
        }

        // Reposition star meter container (below topbar, aligned right with topbar)
        if (this.starMeterContainer) {
            // Calculate topbar dimensions to align properly (using updated dimensions)
            const moveSectionWidth = 120;
            const taskSectionWidth = 140;
            const scoreSectionWidth = 120;
            const levelSectionWidth = 120;
            const sectionGap = 10;
            const totalTopbarWidth = moveSectionWidth + taskSectionWidth + scoreSectionWidth + levelSectionWidth + (sectionGap * 3);

            // Align right edge of star meter with right edge of topbar
            const starMeterX = this.boardCenterX + (totalTopbarWidth / 2) - 100; // 100 is half of star meter width (200px)
            const starMeterY = this.boardCenterY - (this.boardHeight / 2) + 10; // 10px below topbar
            this.starMeterContainer.setPosition(starMeterX, starMeterY);
        }

        // Reposition menu button
        this.repositionMenuButton();
    }

    /**
     * Calculate objectives panel width consistently
     */
    private calculateObjectivesPanelWidth(numObjectives: number): number {
        const objectiveWidth = 90; // Width per objective
        const padding = 20; // Padding on sides
        return (numObjectives * objectiveWidth) + padding;
    }

    /**
     * Reposition moves container relative to objectives panel
     */
    private repositionMovesContainer(): void {
        if (this.movesContainer && this.objectivesContainer) {
            // Calculate panel width using consistent method
            const numObjectives = this.objectiveTexts.length;
            const panelWidth = this.calculateObjectivesPanelWidth(numObjectives);

            // Position relative to objectives container using dynamic positioning
            const objectivesContainerX = this.boardCenterX;
            const objectivesContainerY = this.boardCenterY - (this.boardHeight / 2) - 80;

            const movesX = objectivesContainerX + (panelWidth / 2) + 50; // 50px gap from panel edge
            const movesY = objectivesContainerY + 15; // Same height as objectives panel center (15px offset in panel)
            this.movesContainer.setPosition(movesX, movesY);
        }
    }

    /**
     * Tạo UI elements
     */
    private createUI(): void {
        // Create new topbar with all main UI elements
        this.createTopbar();

        // Create star meter below topbar
        this.createStarMeter();

        // Create other UI elements (hidden/minimal)
        this.createComboUI();
        this.createHighScoreUI();

        // Create menu button and modal
        this.createMenuButton();
        this.createGameMenuModal();
    }
    
    // Remove old UI background - no longer needed
    
    /**
     * Tạo Topbar UI (chứa move, objectives, score, level)
     */
    private createTopbar(): void {
        // Position topbar above board
        const x = this.boardCenterX;
        const y = this.boardCenterY - (this.boardHeight / 2) - 80;

        // Create main topbar container
        this.topbarContainer = this.scene.add.container(x, y);
        this.topbarContainer.setDepth(1000);

        // Topbar dimensions (increase TASK section width for better objectives spacing)
        const moveSectionWidth = 120;
        const taskSectionWidth = 140; // Increased from 120 to 140 for better objectives spacing
        const scoreSectionWidth = 120;
        const levelSectionWidth = 120;
        const sectionHeight = 80;
        const sectionGap = 10;
        const totalWidth = moveSectionWidth + taskSectionWidth + scoreSectionWidth + levelSectionWidth + (sectionGap * 3);

        // Create topbar background
        const topbarBg = this.scene.add.rectangle(0, 0, totalWidth + 20, sectionHeight + 20, 0x1a1a1a, 0.9);
        topbarBg.setStrokeStyle(3, 0x444444);
        this.topbarContainer.add(topbarBg);

        // Calculate section positions
        const startX = -(totalWidth / 2) + (moveSectionWidth / 2);

        // Create MOVE section
        this.createMovesSection(startX, 0, moveSectionWidth, sectionHeight);

        // Create TASK (Objectives) section
        this.createObjectivesSection(startX + moveSectionWidth + sectionGap, 0, taskSectionWidth, sectionHeight);

        // Create SCORE section
        this.createScoreSection(startX + moveSectionWidth + sectionGap + taskSectionWidth + sectionGap, 0, scoreSectionWidth, sectionHeight);

        // Create LEVEL section
        this.createLevelSection(startX + moveSectionWidth + sectionGap + taskSectionWidth + sectionGap + scoreSectionWidth + sectionGap, 0, levelSectionWidth, sectionHeight);
    }

    /**
     * Tạo Moves Section trong topbar
     */
    private createMovesSection(x: number, y: number, width: number, height: number): void {
        this.movesSection = this.scene.add.container(x, y);

        // Section background
        const bg = this.scene.add.rectangle(0, 0, width, height, 0x2a2a2a, 0.8);
        bg.setStrokeStyle(2, 0x555555);
        this.movesSection.add(bg);

        // Title
        const title = this.scene.add.text(0, -25, 'MOVE', {
            fontSize: '14px',
            fontFamily: 'Arial Black',
            color: '#00bfff',
            stroke: '#000000',
            strokeThickness: 1
        });
        title.setOrigin(0.5);
        this.movesSection.add(title);

        // Moves text
        this.movesText = this.scene.add.text(0, 5, '30', {
            fontSize: '28px',
            fontFamily: 'Arial Black',
            color: '#ff6600',
            stroke: '#000000',
            strokeThickness: 2
        });
        this.movesText.setOrigin(0.5);
        this.movesSection.add(this.movesText);

        this.topbarContainer?.add(this.movesSection);
    }

    /**
     * Tạo Objectives Section trong topbar
     */
    private createObjectivesSection(x: number, y: number, width: number, height: number): void {
        this.objectivesSection = this.scene.add.container(x, y);

        // Section background
        const bg = this.scene.add.rectangle(0, 0, width, height, 0x2a2a2a, 0.8);
        bg.setStrokeStyle(2, 0x555555);
        this.objectivesSection.add(bg);

        // Title
        const title = this.scene.add.text(0, -25, 'TASK', {
            fontSize: '14px',
            fontFamily: 'Arial Black',
            color: '#00bfff',
            stroke: '#000000',
            strokeThickness: 1
        });
        title.setOrigin(0.5);
        this.objectivesSection.add(title);

        // Objectives will be added dynamically in updateObjectivesDisplay
        this.topbarContainer?.add(this.objectivesSection);
    }

    /**
     * Tạo Score Section trong topbar
     */
    private createScoreSection(x: number, y: number, width: number, height: number): void {
        this.scoreSection = this.scene.add.container(x, y);

        // Section background
        const bg = this.scene.add.rectangle(0, 0, width, height, 0x2a2a2a, 0.8);
        bg.setStrokeStyle(2, 0x555555);
        this.scoreSection.add(bg);

        // Title
        const title = this.scene.add.text(0, -25, 'SCORE', {
            fontSize: '14px',
            fontFamily: 'Arial Black',
            color: '#00bfff',
            stroke: '#000000',
            strokeThickness: 1
        });
        title.setOrigin(0.5);
        this.scoreSection.add(title);

        // Score text (start with 0, will be updated by score events)
        this.scoreText = this.scene.add.text(0, 5, '0', {
            fontSize: '20px',
            fontFamily: 'Arial Black',
            color: '#ff6600',
            stroke: '#000000',
            strokeThickness: 2
        });
        this.scoreText.setOrigin(0.5);
        this.scoreSection.add(this.scoreText);

        this.topbarContainer?.add(this.scoreSection);
    }

    /**
     * Tạo Level Section trong topbar
     */
    private createLevelSection(x: number, y: number, width: number, height: number): void {
        this.levelSection = this.scene.add.container(x, y);

        // Section background
        const bg = this.scene.add.rectangle(0, 0, width, height, 0x2a2a2a, 0.8);
        bg.setStrokeStyle(2, 0x555555);
        this.levelSection.add(bg);

        // Title
        const title = this.scene.add.text(0, -25, 'LEVEL', {
            fontSize: '14px',
            fontFamily: 'Arial Black',
            color: '#00bfff',
            stroke: '#000000',
            strokeThickness: 1
        });
        title.setOrigin(0.5);
        this.levelSection.add(title);

        // Level text
        this.levelText = this.scene.add.text(0, 5, '186', {
            fontSize: '28px',
            fontFamily: 'Arial Black',
            color: '#ff6600',
            stroke: '#000000',
            strokeThickness: 2
        });
        this.levelText.setOrigin(0.5);
        this.levelSection.add(this.levelText);

        this.topbarContainer?.add(this.levelSection);
    }

    /**
     * Tạo Score UI (will be added to level+star card)
     */
    private createScoreUI(): void {
        // Score will be created in createStarMeter as part of the level card
        // This is just a placeholder for the scoreText reference
    }
    
    // Level UI will be part of star meter now
    
    /**
     * Tạo Moves UI (next to objectives panel, power-up style)
     */
    private createMovesUI(): void {
        // Will be positioned dynamically in updateObjectivesDisplay
        // to be next to the adaptive objectives panel

        // Create moves container for easier positioning
        this.movesContainer = this.scene.add.container(0, 0);
        this.movesContainer.setDepth(1000);

        // Create power-up style circular background
        const movesCircle = this.scene.add.circle(0, 0, 35, 0x1a1a1a, 0.9);
        movesCircle.setStrokeStyle(3, 0x444444);
        this.movesContainer.add(movesCircle);

        // Add inner glow like power-up panels
        const innerGlow = this.scene.add.circle(0, 0, 32, 0x333333, 0.3);
        innerGlow.setStrokeStyle(1, 0x666666);
        this.movesContainer.add(innerGlow);

        // Moves text
        this.movesText = this.scene.add.text(0, 0, '30', {
            fontSize: '24px',
            fontFamily: 'Arial Black',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        });
        this.movesText.setOrigin(0.5);
        this.movesContainer.add(this.movesText);
    }
    
    /**
     * Tạo Objectives UI (above board, power-up panel style)
     */
    private createObjectivesUI(): void {
        // Position above board dynamically
        const x = this.boardCenterX;
        const y = this.boardCenterY - (this.boardHeight / 2) - 80;

        // Create container for objectives centered above board
        this.objectivesContainer = this.scene.add.container(x, y);
        this.objectivesContainer.setDepth(1000);

        // Background will be created dynamically in updateObjectivesDisplay
        // to adapt to number of objectives
    }
    
    /**
     * Tạo Combo UI (hidden)
     */
    private createComboUI(): void {
        // Hidden combo tracking
        this.comboText = this.scene.add.text(-100, -100, 'x1.0', this.TEXT_STYLE);
        this.comboText.setVisible(false);
    }
    
    /**
     * Tạo High Score UI (hidden)
     */
    private createHighScoreUI(): void {
        // Hidden high score tracking
        this.highScoreText = this.scene.add.text(-100, -100, '0', this.TEXT_STYLE);
        this.highScoreText.setVisible(false);
    }

    /**
     * Tạo Star Meter UI (left side card, power-up style)
     */
    private createStarMeter(): void {
        // Position below topbar, aligned right with topbar
        // Calculate topbar dimensions to align properly (using updated dimensions)
        const moveSectionWidth = 120;
        const taskSectionWidth = 140;
        const scoreSectionWidth = 120;
        const levelSectionWidth = 120;
        const sectionGap = 10;
        const totalTopbarWidth = moveSectionWidth + taskSectionWidth + scoreSectionWidth + levelSectionWidth + (sectionGap * 3);

        // Align right edge of star meter with right edge of topbar
        const x = this.boardCenterX + (totalTopbarWidth / 2) - 100; // 100 is half of star meter width (200px)
        const y = this.boardCenterY - (this.boardHeight / 2) + 10; // 10px below topbar (was -20, now +10 = 30px lower)

        // Create star meter container
        this.starMeterContainer = this.scene.add.container(x, y);
        this.starMeterContainer.setDepth(1000);

        // Create star meter (simplified, just the meter)
        this.starMeter = new StarMeter(this.scene, -100, 0);
        this.starMeterContainer.add(this.starMeter.getContainer());
    }

    /**
     * Tạo Menu Button (góc trên phải của màn hình, ngoài board area)
     */
    private createMenuButton(): void {
        // Position at top-right corner of screen, outside board area
        const x = 1024 - 60; // 60px from right edge of screen
        const y = 60; // 60px from top edge of screen

        this.menuButton = this.scene.add.container(x, y);
        this.menuButton.setDepth(1000);

        // Button background (circular)
        const buttonBg = this.scene.add.circle(0, 0, 25, 0x333333, 0.9);
        buttonBg.setStrokeStyle(2, 0x666666);
        buttonBg.setInteractive();
        this.menuButton.add(buttonBg);

        // Menu icon (3 horizontal lines)
        for (let i = 0; i < 3; i++) {
            const line = this.scene.add.rectangle(0, -8 + (i * 8), 20, 2, 0xffffff);
            this.menuButton.add(line);
        }

        // Hover effects
        buttonBg.on('pointerover', () => {
            buttonBg.setFillStyle(0x555555);
            this.scene.tweens.add({
                targets: this.menuButton,
                scaleX: 1.1,
                scaleY: 1.1,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        buttonBg.on('pointerout', () => {
            buttonBg.setFillStyle(0x333333);
            this.scene.tweens.add({
                targets: this.menuButton,
                scaleX: 1,
                scaleY: 1,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        // Click handler
        buttonBg.on('pointerdown', () => {
            this.scene.tweens.add({
                targets: this.menuButton,
                scaleX: 0.9,
                scaleY: 0.9,
                duration: 100,
                yoyo: true,
                onComplete: () => {
                    // Show game menu modal
                    if (this.gameMenuModal) {
                        this.gameMenuModal.show();
                    }
                }
            });
        });
    }

    /**
     * Tạo Game Menu Modal
     */
    private createGameMenuModal(): void {
        this.gameMenuModal = new GameMenuModal(this.scene);
    }

    /**
     * Reposition menu button - fixed position at screen corner
     */
    private repositionMenuButton(): void {
        if (this.menuButton) {
            // Fixed position at top-right corner, independent of board size
            const x = 1024 - 60; // 60px from right edge of screen
            const y = 60; // 60px from top edge of screen
            this.menuButton.setPosition(x, y);
        }
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('score-updated', this.onScoreUpdated, this);
        this.scene.events.on('moves-updated', this.onMovesUpdated, this);
        this.scene.events.on('level-started', this.onLevelStarted, this);
        this.scene.events.on('objective-updated', this.onObjectiveUpdated, this);
        this.scene.events.on('level-completed', this.onLevelCompleted, this);
        this.scene.events.on('level-failed', this.onLevelFailed, this);
        this.scene.events.on('game-over', this.onGameOver, this);
        this.scene.events.on('stars-updated', this.onStarsUpdated, this);
    }
    
    /**
     * Update score display
     */
    private onScoreUpdated(data: { score: number, addedScore: number, combo: number, multiplier: number }): void {
        if (this.scoreText) {
            // Format score for topbar (no "SCORE:" prefix)
            this.scoreText.setText(`${data.score}`);

            // Animate score increase
            this.animateScoreIncrease(data.addedScore);
        }
        
        if (this.comboText && data.combo > 0) {
            this.comboText.setText(`x${data.multiplier.toFixed(1)}`);
            this.comboText.setColor('#ff6600'); // Orange for combo
            
            // Reset color after animation
            this.scene.time.delayedCall(1000, () => {
                if (this.comboText) {
                    this.comboText.setColor('#ffffff');
                }
            });
        } else if (this.comboText) {
            this.comboText.setText('x1.0');
            this.comboText.setColor('#ffffff');
        }
    }
    
    /**
     * Animate score increase
     */
    private animateScoreIncrease(addedScore: number): void {
        if (!this.scoreText) return;
        
        // Create floating score text
        const floatingScore = this.scene.add.text(
            this.scoreText.x + 100,
            this.scoreText.y,
            `+${addedScore}`,
            {
                fontSize: '20px',
                fontFamily: 'Arial, sans-serif',
                color: '#00ff00',
                stroke: '#000000',
                strokeThickness: 2
            }
        );
        
        floatingScore.setDepth(1001);
        
        // Animate floating score
        this.scene.tweens.add({
            targets: floatingScore,
            y: floatingScore.y - 50,
            alpha: 0,
            duration: 1000,
            ease: 'Power2.easeOut',
            onComplete: () => {
                floatingScore.destroy();
            }
        });
        
        // Pulse score text
        this.scene.tweens.add({
            targets: this.scoreText,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            yoyo: true,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Update moves display
     */
    private onMovesUpdated(movesRemaining: number): void {
        if (this.movesText) {
            this.movesText.setText(movesRemaining.toString());

            // Change color based on remaining moves (keep orange as default for topbar)
            if (movesRemaining <= 5) {
                this.movesText.setColor('#ff0000'); // Red for low moves
            } else if (movesRemaining <= 10) {
                this.movesText.setColor('#ffaa00'); // Orange for medium moves
            } else {
                this.movesText.setColor('#ff6600'); // Orange for plenty moves (topbar style)
            }
        }
    }

    /**
     * Handle level started
     */
    private onLevelStarted(data: { levelId: number, levelName: string, objectives: any[] }): void {
        if (this.levelText) {
            // Format level text as just the number for topbar
            this.levelText.setText(`${data.levelId}`);
        }

        this.updateObjectivesDisplay(data.objectives);
        // Reset star meter visual; StarRatingService will emit stars-updated soon after
        this.lastStarsCount = 0;
        this.starMeter?.update(0, 0);
    }

    /**
     * Handle objective updates
     */
    private onObjectiveUpdated(data: { progress: any[], allCompleted: boolean }): void {
        this.updateObjectivesProgress(data.progress);
    }

    /**
     * Update objectives display (now in topbar)
     */
    private updateObjectivesDisplay(objectives: any[]): void {
        // Clear existing objective texts and images
        this.objectiveTexts.forEach(text => text.destroy());
        this.objectiveTexts = [];

        if (!this.objectivesSection) return;

        // Clear existing objective children (keep background and title)
        const children = this.objectivesSection.list.slice();
        children.forEach((child, index) => {
            if (index > 1) { // Keep first 2 (background and title)
                child.destroy();
            }
        });

        // Calculate layout for objectives in the section (improved spacing)
        const maxObjectives = Math.min(objectives.length, 3); // Limit to 3 for space
        const objectiveSpacing = 35; // Increased from 25 to 35 for better spacing
        const startX = -(maxObjectives - 1) * objectiveSpacing / 2;

        // Create objective displays
        objectives.slice(0, maxObjectives).forEach((objective, index) => {
            const objX = startX + (index * objectiveSpacing);
            const objContainer = this.scene.add.container(objX, 5);

            // Create objective image (gem or stone overlay icon)
            let objectiveImage: Phaser.GameObjects.Image;

            if (objective.type === 'destroy-stone-overlays') {
                // Use stone overlay texture for stone overlay objectives
                objectiveImage = this.scene.add.image(0, -5, 'stone_overlay');
                objectiveImage.setScale(0.15); // Smaller for topbar
            } else {
                // Use gem texture for collect objectives with atlas support
                const { texture, frame } = this.getGemTextureWithAtlas(objective.gemType);
                objectiveImage = this.scene.add.image(0, -5, texture, frame);
                objectiveImage.setScale(0.2); // Smaller for topbar
            }

            objectiveImage.setOrigin(0.5, 0.5);
            objContainer.add(objectiveImage);

            // Create objective text below gem
            const objectiveText = this.scene.add.text(
                0,
                8,
                `0/${objective.count}`,
                {
                    fontSize: '10px',
                    fontFamily: 'Arial Black',
                    color: '#ffffff',
                    stroke: '#000000',
                    strokeThickness: 1
                }
            );
            objectiveText.setOrigin(0.5, 0.5);
            objContainer.add(objectiveText);

            this.objectiveTexts.push(objectiveText);
            this.objectivesSection.add(objContainer);
        });
    }

    /**
     * Update objectives progress
     */
    private updateObjectivesProgress(progress: any[]): void {
        progress.forEach((objective, index) => {
            if (this.objectiveTexts[index]) {
                const color = objective.completed ? '#00ff00' : '#ffffff';
                const text = `${objective.collected}/${objective.needed}`;

                this.objectiveTexts[index].setText(text);
                this.objectiveTexts[index].setColor(color);

                // Animate when completed
                if (objective.completed) {
                    this.scene.tweens.add({
                        targets: this.objectiveTexts[index],
                        scaleX: 1.2,
                        scaleY: 1.2,
                        duration: 300,
                        yoyo: true,
                        ease: 'Back.easeOut'
                    });
                }
            }
        });
    }

    /**
     * Get gem texture name for display (legacy method)
     */
    private getGemTexture(gemType: string): string {
        const textures: { [key: string]: string } = {
            'red': 'gem_red',
            'blue': 'gem_blue',
            'green': 'gem_green',
            'yellow': 'gem_yellow',
            'purple': 'gem_purple',
            'orange': 'gem_orange',
            'white': 'gem_white'
        };
        return textures[gemType] || 'gem_red';
    }

    /**
     * Get gem texture with atlas support
     */
    private getGemTextureWithAtlas(gemType: string): { texture: string, frame?: string } {
        // Check if atlas is available
        const atlasAvailable = this.scene.registry.get('atlasAvailable') && this.scene.textures.exists('game_atlas');

        const textures: { [key: string]: string } = {
            'red': 'gem_red',
            'blue': 'gem_blue',
            'green': 'gem_green',
            'yellow': 'gem_yellow',
            'purple': 'gem_purple',
            'orange': 'gem_orange',
            'white': 'gem_white'
        };

        const frameName = textures[gemType] || 'gem_red';

        if (atlasAvailable) {
            return { texture: 'game_atlas', frame: frameName };
        } else {
            return { texture: frameName };
        }
    }
    
    /**
     * Handle level completion
     */
    private onLevelCompleted(data: { levelId: number, movesUsed: number, movesRemaining: number }): void {
        // Show level complete message
        this.showLevelCompleteMessage(data.levelId);
    }

    /**
     * Handle level failed
     */
    private onLevelFailed(data: { levelId: number, reason: string }): void {
        this.showLevelFailedMessage(data.levelId, data.reason);
    }
    
    /**
     * Show level complete message
     */
    private showLevelCompleteMessage(levelId: number): void {
        const message = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            `LEVEL ${levelId} COMPLETE!\nAll objectives achieved!`,
            {
                fontSize: '32px',
                fontFamily: 'Arial, sans-serif',
                color: '#00ff00',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            }
        );

        message.setOrigin(0.5);
        message.setDepth(1002);

        // Animate message
        this.scene.tweens.add({
            targets: message,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            yoyo: true,
            onComplete: () => {
                this.scene.time.delayedCall(2000, () => {
                    message.destroy();
                });
            }
        });
    }

    /**
     * Show level failed message
     */
    private showLevelFailedMessage(levelId: number, reason: string): void {
        let reasonText = '';
        switch (reason) {
            case 'out_of_moves':
                reasonText = 'Out of moves!';
                break;
            default:
                reasonText = 'Try again!';
        }

        const message = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            `LEVEL ${levelId} FAILED\n${reasonText}`,
            {
                fontSize: '32px',
                fontFamily: 'Arial, sans-serif',
                color: '#ff0000',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            }
        );

        message.setOrigin(0.5);
        message.setDepth(1002);

        // Animate message
        this.scene.tweens.add({
            targets: message,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            yoyo: true,
            onComplete: () => {
                this.scene.time.delayedCall(2000, () => {
                    message.destroy();
                });
            }
        });
    }
    
    /**
     * Handle game over
     */
    private onGameOver(data: { finalScore: number, level: number, isHighScore: boolean }): void {
        const message = data.isHighScore ? 
            `GAME OVER!\nNew High Score: ${data.finalScore}\nLevel Reached: ${data.level}` :
            `GAME OVER!\nFinal Score: ${data.finalScore}\nLevel Reached: ${data.level}`;
        
        const gameOverText = this.scene.add.text(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            message,
            {
                fontSize: '28px',
                fontFamily: 'Arial, sans-serif',
                color: data.isHighScore ? '#ffff00' : '#ff6666',
                stroke: '#000000',
                strokeThickness: 3,
                align: 'center'
            }
        );
        
        gameOverText.setOrigin(0.5);
        gameOverText.setDepth(1002);
    }
    
    /**
     * Update high score display
     */
    public updateHighScore(highScore: number): void {
        if (this.highScoreText) {
            this.highScoreText.setText(highScore.toString());
        }
    }
    
    /**
     * Reset UI to initial state
     */
    public resetUI(): void {
        if (this.scoreText) this.scoreText.setText('0');
        if (this.levelText) this.levelText.setText('Level 1');
        if (this.movesText) this.movesText.setText('0');
        if (this.comboText) this.comboText.setText('x1.0');

        // Clear objectives
        this.objectiveTexts.forEach(text => text.destroy());
        this.objectiveTexts = [];
    }

    /**
     * Handle stars updated
     */
    private onStarsUpdated(data: { stars: number; progressPercent: number; starChanged?: boolean; score?: number }): void {
        // Always update meter fill and icons
        this.starMeter?.update(data.stars, data.progressPercent);

        // Flying star animation when a new star is earned
        if (data.starChanged && data.stars > this.lastStarsCount && this.scoreText && this.starMeter) {
            const start = this.getScoreWorldPosition();
            // Animate for each newly gained star (in case multiple thresholds skipped)
            for (let i = this.lastStarsCount; i < data.stars; i++) {
                const delay = (i - this.lastStarsCount) * 200;
                this.scene.time.delayedCall(delay, () => {
                    this.starMeter?.animateStarEarned(i, start.x + 80, start.y);
                });
            }
        }

        this.lastStarsCount = data.stars;
    }

    private getScoreWorldPosition(): { x: number; y: number } {
        if (!this.scoreText) return { x: 0, y: 0 };
        const m = this.scoreText.getWorldTransformMatrix();
        const p = new Phaser.Math.Vector2();
        m.transformPoint(0, 0, p);
        return { x: p.x, y: p.y };
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('score-updated', this.onScoreUpdated, this);
        this.scene.events.off('moves-updated', this.onMovesUpdated, this);
        this.scene.events.off('level-started', this.onLevelStarted, this);
        this.scene.events.off('objective-updated', this.onObjectiveUpdated, this);
        this.scene.events.off('level-completed', this.onLevelCompleted, this);
        this.scene.events.off('level-failed', this.onLevelFailed, this);
        this.scene.events.off('game-over', this.onGameOver, this);
        this.scene.events.off('stars-updated', this.onStarsUpdated, this);

        // Clean up objectives
        this.objectiveTexts.forEach(text => text.destroy());
        this.objectiveTexts = [];

        if (this.uiContainer) {
            this.uiContainer.destroy();
        }

        // Cleanup menu modal
        if (this.gameMenuModal) {
            this.gameMenuModal.destroy();
        }
    }
}
