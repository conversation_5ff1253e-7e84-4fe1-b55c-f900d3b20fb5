# Product Requirements Document (PRD) - Match 3 Game với Phaser

## 1. Tổng quan dự án

### 1.1 <PERSON><PERSON> tả sản phẩm
Game Match 3 là một trò chơi giải đố kinh điển được phát triển bằng Phaser.js framework. Người chơi sẽ hoán đổi các viên gem để tạo thành hàng ngang hoặc dọc có ít nhất 3 viên cùng loại để ghi điểm.

### 1.2 M<PERSON><PERSON> tiêu
- Tạo ra một game match 3 hoàn chỉnh với gameplay mượt mà
- Sử dụng hình ảnh tùy chỉnh của riêng bạn
- Tối ưu hóa hiệu suất và trải nghiệm người dùng
- Hỗ trợ cả desktop và mobile

### 1.3 Công nghệ sử dụng
- **Framework**: @phaserjs/game@latest
- **Package Manager**: Bun
- **Ngô<PERSON> ngữ**: JavaScript/TypeScript
- **Bundler**: Webpack hoặc Vite
- **Assets**: Hình ảnh tùy chỉnh (png)

## 2. <PERSON><PERSON><PERSON> năng chính

### 2.1 Core Gameplay
- **Grid System**: Lưới 8x8 hoặc 9x9 viên gem
- **Swap Mechanism**: Hoán đổi 2 viên gem liền kề
- **Match Detection**: Phát hiện 3+ viên cùng loại theo hàng/cột
- **Gravity System**: Viên gem rơi xuống khi có khoảng trống
- **Refill System**: Tạo viên gem mới từ trên xuống

### 2.2 Visual Effects
- **Smooth Animations**: Sử dụng Phaser Tweens cho chuyển động mượt
- **Particle Effects**: Hiệu ứng khi match thành công
- **Glow Effects**: Highlight viên gem được chọn
- **Cascade Animations**: Hiệu ứng dây chuyền khi nhiều match

### 2.3 Game Mechanics
- **Score System**: Tính điểm dựa trên số viên match và combo
- **Combo System**: Điểm nhân lên khi có match liên tiếp
- **Special Gems**: Viên gem đặc biệt khi match 4+ viên
- **Power-ups**: Các kỹ năng đặc biệt (bomb, lightning, etc.)

### 2.4 UI/UX Features
- **Responsive Design**: Tự động điều chỉnh theo kích thước màn hình
- **Touch Support**: Hỗ trợ cảm ứng cho mobile
- **Visual Feedback**: Hiệu ứng khi hover/click
- **Sound Effects**: Âm thanh cho các hành động

## 3. Kiến trúc kỹ thuật

### 3.1 Scene Structure
```
- BootScene: Khởi tạo game
- PreloadScene: Load assets
- MenuScene: Menu chính
- GameScene: Gameplay chính
- UIScene: Overlay UI
- PauseScene: Menu pause
```

### 3.2 Core Classes
```
- GameBoard: Quản lý grid và logic game
- Gem: Class cho từng viên gem
- MatchDetector: Phát hiện match
- ScoreManager: Quản lý điểm số
- EffectManager: Quản lý hiệu ứng
- InputHandler: Xử lý input
```

### 3.3 Asset Management
- **Texture Atlas**: Sử dụng sprite sheet cho gems
- **Custom Images**: Hỗ trợ PNG/SVG tùy chỉnh
- **Responsive Scaling**: Tự động scale theo device
- **Memory Optimization**: Quản lý memory hiệu quả

## 4. Gameplay Flow

### 4.1 Game Loop
1. Hiển thị grid với gems ngẫu nhiên
2. Chờ input từ người chơi
3. Validate move hợp lệ
4. Thực hiện swap animation
5. Detect và remove matches
6. Apply gravity và refill
7. Tính điểm và update UI
8. Repeat từ bước 2

### 4.2 Input Handling
- **Mouse**: Click và drag để swap
- **Touch**: Touch và swipe cho mobile
- **Keyboard**: Arrow keys cho accessibility
- **Visual Feedback**: Highlight selection

### 4.3 Match Logic
- Scan horizontal và vertical
- Detect L-shape và T-shape matches
- Special gem creation rules
- Cascade match detection

## 5. Technical Specifications

### 5.1 Performance Requirements
- **FPS**: Duy trì 60 FPS trên desktop, 30+ FPS trên mobile
- **Memory**: < 100MB RAM usage
- **Load Time**: < 3 giây cho initial load
- **Responsive**: Smooth trên các device khác nhau

### 5.2 Browser Support
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- Mobile browsers (iOS Safari, Chrome Mobile)

### 5.3 Asset Specifications
- **Gem Images**: 128x128px PNG với alpha channel
- **Background**: 1920x1080px hoặc tỷ lệ 16:9
- **UI Elements**: Vector SVG hoặc high-res PNG
- **Audio**: MP3/OGG format, < 1MB per file

## 6. Development Phases

### Phase 1: Core Setup (1-2 ngày)
- Setup Phaser project với Bun
- Tạo basic scene structure
- Implement grid system
- Basic gem rendering

### Phase 2: Gameplay Logic (3-4 ngày)
- Implement swap mechanism
- Match detection algorithm
- Gravity và refill system
- Basic scoring

### Phase 3: Visual Polish (2-3 ngày)
- Animation system với Tweens
- Particle effects
- UI/UX improvements
- Custom asset integration

### Phase 4: Advanced Features (2-3 ngày)
- Special gems (4+ matches tạo power-ups)
- Power-up effects (bombs, lightning, etc.)
- Combo system
- Sound integration
- Mobile optimization

### Phase 5: Testing & Polish (1-2 ngày)
- Bug fixes
- Performance optimization
- Cross-browser testing
- Final polish

## 7. File Structure
```
match3/
├── src/
│   ├── scenes/
│   ├── objects/
│   ├── managers/
│   ├── utils/
│   └── main.js
├── assets/
│   ├── images/
│   ├── audio/
│   └── data/
├── dist/
└── package.json
```

## 8. Success Metrics
- Smooth 60 FPS gameplay
- Responsive trên mọi device
- Intuitive controls
- Engaging visual effects
- Stable performance
- Clean, maintainable code

## 9. Risks & Mitigation
- **Performance**: Optimize asset loading và rendering
- **Browser Compatibility**: Extensive testing
- **Mobile Touch**: Careful touch event handling
- **Memory Leaks**: Proper cleanup và pooling

## 10. Next Steps
1. Setup development environment
2. Create basic project structure
3. Implement core gameplay loop
4. Integrate custom assets
5. Add visual effects và polish
6. Testing và optimization
