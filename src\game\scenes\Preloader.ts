import { Scene } from 'phaser';
import { AssetGenerator } from '../../utils/AssetGenerator';

export class Preloader extends Scene
{
    constructor ()
    {
        super('Preloader');
    }

    init ()
    {
        //  We loaded this image in our Boot Scene, so we can display it here
        this.add.image(512, 384, 'background');

        //  A simple progress bar. This is the outline of the bar.
        this.add.rectangle(512, 384, 468, 32).setStrokeStyle(1, 0xffffff);

        //  This is the progress bar itself. It will increase in size from the left based on the % of progress.
        const bar = this.add.rectangle(512-230, 384, 4, 28, 0xffffff);

        //  Use the 'progress' event emitted by the LoaderPlugin to update the loading bar
        this.load.on('progress', (progress: number) => {

            //  Update the progress bar (our bar is 464px wide, so 100% = 464px)
            bar.width = 4 + (460 * progress);

        });
    }

    preload ()
    {
        //  Set path to assets folder
        this.load.setPath('assets');

        // Load level data JSON (Phase 3)
        this.load.json('levels', 'data/levels.json');

        // Load audio files
        this.loadAudioAssets();

        // Load existing assets
        this.load.image('logo', 'logo.png');

        // Load sprite atlas (primary approach)
        this.load.atlas('game_atlas', 'game_atlas.png', 'game_atlas.json');

        // Obstacles use generated textures only - no custom loading needed

        // Handle load errors gracefully
        this.load.on('loaderror', (file: any) => {
            console.log('Failed to load:', file.key);
        });
    }

    /**
     * Load all audio assets
     */
    private loadAudioAssets(): void {
        console.log('Loading audio assets...');

        // Background music
        this.load.audio('background_music', 'audio/background_music.mp3');

        // UI sounds
        this.load.audio('button_click', 'audio/button_click.mp3');

        // Gameplay sounds
        this.load.audio('gem_swap', 'audio/gem_swap.mp3');
        this.load.audio('match_small', 'audio/match_small.mp3');
        this.load.audio('match_medium', 'audio/match_medium.mp3');
        this.load.audio('match_large', 'audio/match_large.mp3');

        // Special effects
        this.load.audio('special_gem_activate', 'audio/special_gem_activate.mp3');
        this.load.audio('power_up_collect', 'audio/power_up_collect.mp3');
        this.load.audio('power_up_use', 'audio/power_up_use.mp3');

        // Combo sounds
        this.load.audio('combo_low', 'audio/combo_low.mp3');
        this.load.audio('combo_high', 'audio/combo_high.mp3');

        // Level sounds
        this.load.audio('level_start', 'audio/level_start.mp3');
        this.load.audio('level_complete', 'audio/level_complete.mp3');
        this.load.audio('level_fail', 'audio/level_fail.mp3');

        // Star system
        this.load.audio('star_earned', 'audio/star_earned.mp3');
    }

    create ()
    {
        // Check if sprite atlas was loaded successfully
        const atlasLoaded = this.checkAtlasLoaded();

        if (atlasLoaded) {
            console.log('Using sprite atlas for optimized loading');
            this.createAtlasAliases();
        } else {
            console.log('Atlas failed, falling back to generated assets');
            // Generate fallback assets for all game elements
            AssetGenerator.createAllGemTextures(this);
        }

        // Always use generated obstacle textures
        console.log('Using generated obstacle assets');
        AssetGenerator.createAllObstacleTextures(this);

        // Always generate these assets
        AssetGenerator.createBackgroundTexture(this);
        AssetGenerator.createGridCellTexture(this);
        AssetGenerator.createBoardFrameTexture(this);
        AssetGenerator.createSelectionTexture(this);
        AssetGenerator.createParticleTexture(this);
        AssetGenerator.createStoneOverlayTexture(this);

        // Log audio loading status
        this.logAudioStatus();

        //  Move to the MainMenu
        this.scene.start('MainMenu');
    }

    private checkAtlasLoaded(): boolean {
        return this.textures.exists('game_atlas');
    }

    private createAtlasAliases(): void {
        // Simple approach: just mark that atlas is available
        // We'll modify the Gem constructor to use atlas frames directly
        console.log('Atlas loaded successfully - will use atlas frames directly');

        // Set a global flag to indicate atlas is available
        this.registry.set('atlasAvailable', true);
    }





    /**
     * Log audio loading status
     */
    private logAudioStatus(): void {
        console.log('Checking audio loading status...');

        // Check which audio files loaded successfully
        const audioKeys = [
            'background_music', 'button_click', 'gem_swap',
            'match_small', 'match_medium', 'match_large',
            'special_gem_activate', 'level_start', 'level_complete', 'level_fail',
            'power_up_collect', 'power_up_use', 'combo_low', 'combo_high', 'star_earned'
        ];

        let loadedCount = 0;
        audioKeys.forEach(key => {
            if (this.cache.audio.exists(key)) {
                loadedCount++;
            } else {
                console.warn(`Audio file not loaded: ${key}`);
            }
        });

        console.log(`Audio loading complete: ${loadedCount}/${audioKeys.length} files loaded successfully`);
    }
}
