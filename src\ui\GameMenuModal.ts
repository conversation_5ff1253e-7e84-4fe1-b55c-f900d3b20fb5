import { Scene } from 'phaser';

/**
 * Game Menu Modal - hiển thị menu trong game với các options: Back, Mute, Select Level
 */
export class GameMenuModal {
    private scene: Scene;
    private container: Phaser.GameObjects.Container;
    private overlay: Phaser.GameObjects.Rectangle;
    private modalBg: Phaser.GameObjects.Rectangle;
    private isVisible: boolean = false;

    constructor(scene: Scene) {
        this.scene = scene;
        this.createModal();
    }

    /**
     * Tạo modal UI
     */
    private createModal(): void {
        // Create container for entire modal
        this.container = this.scene.add.container(512, 384);
        this.container.setDepth(2000);
        this.container.setVisible(false);

        // Dark overlay
        this.overlay = this.scene.add.rectangle(0, 0, 1024, 768, 0x000000, 0.7);
        this.overlay.setInteractive();
        this.container.add(this.overlay);

        // Modal background
        this.modalBg = this.scene.add.rectangle(0, 0, 400, 300, 0x1a1a1a, 0.95);
        this.modalBg.setStrokeStyle(4, 0x444444);
        this.container.add(this.modalBg);

        // Modal title
        const title = this.scene.add.text(0, -120, 'MENU', {
            fontSize: '32px',
            fontFamily: 'Arial Black',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3
        });
        title.setOrigin(0.5);
        this.container.add(title);

        // Create menu buttons
        this.createMenuButtons();

        // Close modal when clicking overlay
        this.overlay.on('pointerdown', () => {
            this.hide();
        });
    }

    /**
     * Tạo các nút menu
     */
    private createMenuButtons(): void {
        // Back button
        const backButton = this.createMenuButton(0, -40, 'BACK', 0x4CAF50, () => {
            console.log('Back button clicked - TODO: implement back to main menu');
            this.hide();
        });
        this.container.add(backButton);

        // Mute button
        const muteButton = this.createMenuButton(0, 20, 'MUTE', 0xFF9800, () => {
            console.log('Mute button clicked - TODO: implement mute/unmute audio');
            this.hide();
        });
        this.container.add(muteButton);

        // Select Level button
        const levelButton = this.createMenuButton(0, 80, 'SELECT LEVEL', 0x2196F3, () => {
            console.log('Select Level button clicked - TODO: implement level selection');
            this.hide();
        });
        this.container.add(levelButton);
    }

    /**
     * Tạo một nút menu
     */
    private createMenuButton(x: number, y: number, text: string, color: number, callback: () => void): Phaser.GameObjects.Container {
        const buttonContainer = this.scene.add.container(x, y);

        // Button background
        const bg = this.scene.add.rectangle(0, 0, 280, 45, color);
        bg.setStrokeStyle(2, 0xffffff);
        bg.setInteractive();
        buttonContainer.add(bg);

        // Button text
        const buttonText = this.scene.add.text(0, 0, text, {
            fontSize: '18px',
            fontFamily: 'Arial Black',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        });
        buttonText.setOrigin(0.5);
        buttonContainer.add(buttonText);

        // Hover effects
        bg.on('pointerover', () => {
            bg.setFillStyle(color + 0x333333);
            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 1.05,
                scaleY: 1.05,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        bg.on('pointerout', () => {
            bg.setFillStyle(color);
            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 1,
                scaleY: 1,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        // Click handler
        bg.on('pointerdown', () => {
            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 0.95,
                scaleY: 0.95,
                duration: 100,
                yoyo: true,
                onComplete: () => {
                    callback();
                }
            });
        });

        return buttonContainer;
    }

    /**
     * Hiển thị modal
     */
    public show(): void {
        if (this.isVisible) return;

        this.isVisible = true;
        this.container.setVisible(true);

        // Animate modal appearance
        this.container.setScale(0.8);
        this.container.setAlpha(0);

        this.scene.tweens.add({
            targets: this.container,
            scaleX: 1,
            scaleY: 1,
            alpha: 1,
            duration: 300,
            ease: 'Back.easeOut'
        });
    }

    /**
     * Ẩn modal
     */
    public hide(): void {
        if (!this.isVisible) return;

        this.scene.tweens.add({
            targets: this.container,
            scaleX: 0.8,
            scaleY: 0.8,
            alpha: 0,
            duration: 200,
            ease: 'Back.easeIn',
            onComplete: () => {
                this.container.setVisible(false);
                this.isVisible = false;
            }
        });
    }

    /**
     * Kiểm tra modal có đang hiển thị không
     */
    public getIsVisible(): boolean {
        return this.isVisible;
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        if (this.container) {
            this.container.destroy();
        }
    }
}
