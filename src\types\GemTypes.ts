/**
 * Enum cho các loại gem cơ bản và advanced
 */
export enum GemType {
    // Basic gems (unlocked from start)
    RED = 0,
    BLUE = 1,
    GREEN = 2,
    YELLOW = 3,
    PURPLE = 4,
    ORANGE = 5,
    WHITE = 6,

    // Advanced gems (unlocked by level progression)
    DIAMOND = 7,
    RUBY = 8,
    EMERALD = 9,
    SAPPHIRE = 10,
    CRYSTAL = 11,
    MOONSTONE = 12,
    OBSIDIAN = 13,
    FIRE_OPAL = 14,
    ICE_CRYSTAL = 15,
    LIGHTNING = 16
}

/**
 * Enum cho các loại special gem
 */
export enum SpecialGemType {
    NONE = 'none',
    STRIPED_HORIZONTAL = 'striped_horizontal',
    STRIPED_VERTICAL = 'striped_vertical',
    WRAPPED = 'wrapped',
    COLOR_BOMB = 'color_bomb'
}

/**
 * Enum cho các loại match pattern
 */
export enum MatchType {
    THREE = 'three',           // 3 gems in a row/column
    FOUR = 'four',            // 4 gems in a row/column
    FIVE_PLUS = 'five_plus',  // 5+ gems in a row/column
    L_SHAPE = 'l_shape',      // L shape match
    T_SHAPE = 't_shape'       // T shape match
}

/**
 * Interface cho match information
 */
export interface MatchInfo {
    gems: import('../objects/Gem').Gem[];
    type: MatchType;
    direction?: 'horizontal' | 'vertical';
    centerPosition?: { row: number; col: number };
    specialGemType?: SpecialGemType;
}

/**
 * Interface cho special gem properties
 */
export interface SpecialGemProperties {
    type: SpecialGemType;
    baseGemType: GemType;
    canActivate: boolean;
    activationRange?: {
        horizontal?: boolean;
        vertical?: boolean;
        radius?: number;
    };
}

/**
 * Utility functions cho gem types
 */
export class GemTypeUtils {
    /**
     * Get texture name cho gem type
     */
    static getGemTexture(gemType: GemType, specialType: SpecialGemType = SpecialGemType.NONE): string {
        const baseTextures = [
            // Basic gems
            'gem_red', 'gem_blue', 'gem_green', 'gem_yellow',
            'gem_purple', 'gem_orange', 'gem_white',
            // Advanced gems
            'gem_diamond', 'gem_ruby', 'gem_emerald', 'gem_sapphire',
            'gem_crystal', 'gem_moonstone', 'gem_obsidian', 'gem_fire_opal',
            'gem_ice_crystal', 'gem_lightning'
        ];

        const baseTexture = baseTextures[gemType];

        switch (specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
                return 'special_gem_1';
            case SpecialGemType.STRIPED_VERTICAL:
                return 'special_gem_2';
            case SpecialGemType.WRAPPED:
                return 'special_gem_3';
            case SpecialGemType.COLOR_BOMB:
                return 'special_gem_4';
            default:
                return baseTexture;
        }
    }

    /**
     * Get gem color tint
     */
    static getGemColor(gemType: GemType): number {
        const colors = [
            // Basic gems
            0xff4444, // red
            0x4444ff, // blue
            0x44ff44, // green
            0xffff44, // yellow
            0xff44ff, // purple
            0xff8844, // orange
            0xffffff, // white
            // Advanced gems
            0xe6f3ff, // diamond - light blue crystal
            0xcc0000, // ruby - deep red
            0x00cc66, // emerald - emerald green
            0x0066cc, // sapphire - deep blue
            0xccccff, // crystal - light purple
            0xf0f8ff, // moonstone - silver moonlight
            0x1a1a1a, // obsidian - glossy black
            0xff6600, // fire_opal - fire orange
            0x99ddff, // ice_crystal - ice blue
            0xffff00  // lightning - electric yellow
        ];
        return colors[gemType] || 0xffffff;
    }

    /**
     * Determine special gem type từ match info
     */
    static determineSpecialGemType(matchInfo: MatchInfo): SpecialGemType {
        switch (matchInfo.type) {
            case MatchType.FOUR:
                return matchInfo.direction === 'horizontal' 
                    ? SpecialGemType.STRIPED_VERTICAL 
                    : SpecialGemType.STRIPED_HORIZONTAL;
            
            case MatchType.FIVE_PLUS:
                return SpecialGemType.COLOR_BOMB;
            
            case MatchType.L_SHAPE:
            case MatchType.T_SHAPE:
                return SpecialGemType.WRAPPED;
            
            default:
                return SpecialGemType.NONE;
        }
    }

    /**
     * Check if gem type is special
     */
    static isSpecialGem(specialType: SpecialGemType): boolean {
        return specialType !== SpecialGemType.NONE;
    }

    /**
     * Check if gem type is basic (unlocked from start)
     */
    static isBasicGem(gemType: GemType): boolean {
        return gemType >= GemType.RED && gemType <= GemType.WHITE;
    }

    /**
     * Check if gem type is advanced (needs unlock)
     */
    static isAdvancedGem(gemType: GemType): boolean {
        return gemType >= GemType.DIAMOND && gemType <= GemType.LIGHTNING;
    }

    /**
     * Get all basic gem types
     */
    static getBasicGemTypes(): GemType[] {
        return [
            GemType.RED, GemType.BLUE, GemType.GREEN, GemType.YELLOW,
            GemType.PURPLE, GemType.ORANGE, GemType.WHITE
        ];
    }

    /**
     * Get all advanced gem types
     */
    static getAdvancedGemTypes(): GemType[] {
        return [
            GemType.DIAMOND, GemType.RUBY, GemType.EMERALD, GemType.SAPPHIRE,
            GemType.CRYSTAL, GemType.MOONSTONE, GemType.OBSIDIAN, GemType.FIRE_OPAL,
            GemType.ICE_CRYSTAL, GemType.LIGHTNING
        ];
    }

    /**
     * Get gem name for display
     */
    static getGemName(gemType: GemType): string {
        const names = [
            // Basic gems
            'Red', 'Blue', 'Green', 'Yellow', 'Purple', 'Orange', 'White',
            // Advanced gems
            'Diamond', 'Ruby', 'Emerald', 'Sapphire', 'Crystal', 'Moonstone',
            'Obsidian', 'Fire Opal', 'Ice Crystal', 'Lightning'
        ];
        return names[gemType] || 'Unknown';
    }
}
