# Supabase Integration Plan & PRD
## Match 3 Game Cloud Features

### 📋 Product Requirements Document (PRD)

#### **Product Vision**
Transform Match 3 game từ offline single-player sang cloud-enabled social gaming experience với user accounts, progress sync, leaderboards, và daily rewards.

#### **Target Users**
- **Primary**: Casual mobile gamers muốn sync progress across devices
- **Secondary**: Competitive players quan tâm leaderboards và social features

#### **Success Metrics**
- User retention: +40% (7-day), +25% (30-day)
- Daily active users: +60% 
- Session length: +30% (do daily rewards & social features)
- Revenue potential: Foundation cho IAP và premium features

---

### 🎯 Core Features & User Stories

#### **F1: User Authentication**
- **As a player**, I want to create account với email để save progress
- **As a player**, I want to login với Google/Apple để quick access
- **As a player**, I want to play offline và auto-sync khi có internet

#### **F2: Cloud Progress Sync**
- **As a player**, I want progress sync across devices
- **As a player**, I want to see best scores/stars cho mỗi level
- **As a player**, I want to continue từ level cao nhất đã unlock

#### **F3: Leaderboards**
- **As a player**, I want to see top scores cho mỗi level
- **As a player**, I want to compete với friends và global players
- **As a player**, I want to see my ranking position

#### **F4: Daily Rewards & Inventory**
- **As a player**, I want daily login rewards (power-ups)
- **As a player**, I want spin wheel để earn power-ups
- **As a player**, I want inventory để manage power-up items

---

### 🏗️ Technical Architecture

#### **Database Schema (Supabase)**

```sql
-- User profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  display_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Player progress per level
CREATE TABLE player_progress (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  level_id INTEGER NOT NULL,
  best_stars INTEGER DEFAULT 0 CHECK (best_stars >= 0 AND best_stars <= 3),
  best_score INTEGER DEFAULT 0 CHECK (best_score >= 0),
  unlocked BOOLEAN DEFAULT FALSE,
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, level_id)
);

-- Leaderboards per level
CREATE TABLE leaderboards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  level_id INTEGER NOT NULL,
  score INTEGER NOT NULL CHECK (score >= 0),
  stars INTEGER NOT NULL CHECK (stars >= 0 AND stars <= 3),
  moves_used INTEGER NOT NULL CHECK (moves_used >= 0),
  achieved_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, level_id)
);

-- Player inventory (power-ups)
CREATE TABLE player_items (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  item_type TEXT NOT NULL, -- 'hammer', 'color_bomb', 'extra_moves'
  quantity INTEGER DEFAULT 0 CHECK (quantity >= 0),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, item_type)
);

-- Daily rewards tracking
CREATE TABLE daily_rewards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  reward_date DATE NOT NULL,
  reward_type TEXT NOT NULL,
  reward_data JSONB, -- { item_type: 'hammer', quantity: 3 }
  claimed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, reward_date)
);

-- Spin wheel history
CREATE TABLE spin_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  reward_type TEXT NOT NULL,
  reward_data JSONB,
  spun_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### **Row Level Security (RLS) Policies**

```sql
-- Profiles: Users can read all, update own
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Public profiles are viewable by everyone" ON profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- Player Progress: Users can only access own data
ALTER TABLE player_progress ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own progress" ON player_progress FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own progress" ON player_progress FOR ALL USING (auth.uid() = user_id);

-- Leaderboards: Public read, users can insert/update own scores
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Leaderboards are publicly readable" ON leaderboards FOR SELECT USING (true);
CREATE POLICY "Users can insert own scores" ON leaderboards FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own scores" ON leaderboards FOR UPDATE USING (auth.uid() = user_id);

-- Player Items: Users can only access own inventory
ALTER TABLE player_items ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own items" ON player_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own items" ON player_items FOR ALL USING (auth.uid() = user_id);

-- Daily Rewards: Users can only access own rewards
ALTER TABLE daily_rewards ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own rewards" ON daily_rewards FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own rewards" ON daily_rewards FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Spin History: Users can only access own history
ALTER TABLE spin_history ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own spin history" ON spin_history FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own spins" ON spin_history FOR INSERT WITH CHECK (auth.uid() = user_id);
```

---

### 🔧 Implementation Plan

#### **Phase 1: Foundation Setup (Week 1-2)**

**1.1 Supabase Project Setup**
```bash
# Install dependencies
bun add @supabase/supabase-js
bun add -d @types/node

# Environment setup
echo "VITE_SUPABASE_URL=your_supabase_url" >> .env
echo "VITE_SUPABASE_ANON_KEY=your_anon_key" >> .env
```

**1.2 Core Services**
```typescript
// src/services/SupabaseService.ts
export class SupabaseService {
  private static instance: SupabaseService;
  private supabase: SupabaseClient;
  private isOnline: boolean = true;
  
  static getInstance(): SupabaseService { /* singleton */ }
  
  // Auth methods
  async signUp(email: string, password: string): Promise<AuthResponse>
  async signIn(email: string, password: string): Promise<AuthResponse>
  async signInWithOAuth(provider: 'google' | 'apple'): Promise<AuthResponse>
  async signOut(): Promise<void>
  
  // Connection management
  setOnlineStatus(online: boolean): void
  isConnected(): boolean
}

// src/services/CloudSyncService.ts
export class CloudSyncService {
  // Progress sync
  async syncProgress(): Promise<void>
  async uploadProgress(levelId: number, stars: number, score: number): Promise<void>
  async downloadProgress(): Promise<PlayerProgress[]>
  
  // Offline-first with conflict resolution
  private mergeProgress(local: PlayerProgress[], remote: PlayerProgress[]): PlayerProgress[]
}
```

**1.3 Feature Flags**
```typescript
// src/config/FeatureFlags.ts
export const FEATURE_FLAGS = {
  SUPABASE_ENABLED: import.meta.env.VITE_ENABLE_SUPABASE === 'true',
  LEADERBOARDS_ENABLED: import.meta.env.VITE_ENABLE_LEADERBOARDS === 'true',
  DAILY_REWARDS_ENABLED: import.meta.env.VITE_ENABLE_DAILY_REWARDS === 'true'
};
```

#### **Phase 2: Authentication & Progress Sync (Week 3-4)**

**2.1 Auth UI Components**
```typescript
// src/scenes/AuthScene.ts
export class AuthScene extends Phaser.Scene {
  // Login/Register forms
  // OAuth buttons
  // Guest mode option
  // Offline indicator
}

// src/ui/components/AuthModal.ts
export class AuthModal {
  // Email/password forms
  // Social login buttons
  // Error handling
  // Loading states
}
```

**2.2 Enhanced ProgressService**
```typescript
// src/services/ProgressService.ts (enhanced)
export class ProgressService {
  private cloudSync: CloudSyncService;
  private localProgress: Map<number, LevelProgress>;
  
  // Hybrid local + cloud storage
  async saveProgress(levelId: number, stars: number, score: number): Promise<void> {
    // Save locally first (immediate)
    this.saveLocal(levelId, stars, score);
    
    // Upload to cloud (background)
    if (this.cloudSync.isConnected()) {
      await this.cloudSync.uploadProgress(levelId, stars, score);
    }
  }
  
  async loadProgress(): Promise<Map<number, LevelProgress>> {
    // Load local first
    const local = this.loadLocal();
    
    // Sync with cloud if available
    if (this.cloudSync.isConnected()) {
      const remote = await this.cloudSync.downloadProgress();
      return this.mergeProgress(local, remote);
    }
    
    return local;
  }
}
```

#### **Phase 3: Leaderboards (Week 5-6)**

**3.1 Leaderboard Service**
```typescript
// src/services/LeaderboardService.ts
export class LeaderboardService {
  async submitScore(levelId: number, score: number, stars: number, movesUsed: number): Promise<void>
  async getLeaderboard(levelId: number, limit: number = 100): Promise<LeaderboardEntry[]>
  async getUserRank(levelId: number, userId: string): Promise<number>
  async getFriendsLeaderboard(levelId: number): Promise<LeaderboardEntry[]>
}
```

**3.2 Leaderboard UI**
```typescript
// src/scenes/LeaderboardScene.ts
export class LeaderboardScene extends Phaser.Scene {
  // Level selector
  // Global/Friends toggle
  // Rank list with avatars
  // User's position highlight
  // Refresh functionality
}
```

**3.3 Anti-Cheat Measures**
```sql
-- Edge Function: validate_score.sql
CREATE OR REPLACE FUNCTION validate_score(
  level_id INTEGER,
  score INTEGER,
  stars INTEGER,
  moves_used INTEGER
) RETURNS BOOLEAN AS $$
BEGIN
  -- Validate score is reasonable for level
  -- Check moves_used is within level limit
  -- Verify stars match score thresholds
  -- Rate limiting per user
  RETURN TRUE; -- or FALSE if suspicious
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

#### **Phase 4: Daily Rewards & Inventory (Week 7-8)**

**4.1 Daily Rewards System**
```typescript
// src/services/DailyRewardService.ts
export class DailyRewardService {
  async checkDailyReward(): Promise<DailyReward | null>
  async claimDailyReward(): Promise<DailyReward>
  private generateDailyReward(day: number): DailyReward
}

// src/services/InventoryService.ts
export class InventoryService {
  async getInventory(): Promise<Map<string, number>>
  async addItem(itemType: string, quantity: number): Promise<void>
  async useItem(itemType: string, quantity: number): Promise<boolean>
  async syncInventory(): Promise<void>
}
```

**4.2 Spin Wheel Feature**
```typescript
// Edge Function for spin wheel
CREATE OR REPLACE FUNCTION spin_wheel(user_id UUID)
RETURNS JSONB AS $$
DECLARE
  reward JSONB;
  last_spin TIMESTAMP;
BEGIN
  -- Check cooldown (e.g., 1 spin per hour)
  SELECT spun_at INTO last_spin 
  FROM spin_history 
  WHERE user_id = spin_wheel.user_id 
  ORDER BY spun_at DESC 
  LIMIT 1;
  
  IF last_spin > NOW() - INTERVAL '1 hour' THEN
    RAISE EXCEPTION 'Spin cooldown active';
  END IF;
  
  -- Generate random reward
  reward := generate_spin_reward();
  
  -- Record spin and add to inventory
  INSERT INTO spin_history (user_id, reward_type, reward_data, spun_at)
  VALUES (user_id, reward->>'type', reward, NOW());
  
  -- Add to inventory
  INSERT INTO player_items (user_id, item_type, quantity)
  VALUES (user_id, reward->>'type', (reward->>'quantity')::INTEGER)
  ON CONFLICT (user_id, item_type)
  DO UPDATE SET quantity = player_items.quantity + (reward->>'quantity')::INTEGER;
  
  RETURN reward;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

### 🚀 Migration Strategy

#### **Offline-First Approach**
1. **Graceful Degradation**: All features work offline với local storage
2. **Background Sync**: Upload data khi có connection
3. **Conflict Resolution**: Last-write-wins với timestamps
4. **User Experience**: Seamless transition online/offline

#### **Feature Flag Rollout**
```typescript
// Gradual feature enablement
const ROLLOUT_PHASES = {
  PHASE_1: ['auth', 'progress_sync'],
  PHASE_2: ['leaderboards'],
  PHASE_3: ['daily_rewards', 'inventory'],
  PHASE_4: ['social_features']
};
```

#### **Data Migration**
```typescript
// Migrate existing localStorage to Supabase
async function migrateLocalProgress(): Promise<void> {
  const localData = localStorage.getItem('match3_progress_v1');
  if (localData && this.supabase.auth.user()) {
    const progress = JSON.parse(localData);
    await this.cloudSync.uploadBulkProgress(progress);
    localStorage.setItem('match3_migrated', 'true');
  }
}
```

---

### 📊 Analytics & Monitoring

#### **Key Metrics to Track**
- Authentication conversion rate
- Sync success/failure rates  
- Leaderboard engagement
- Daily reward claim rates
- Inventory usage patterns
- Offline vs online play time

#### **Error Monitoring**
```typescript
// src/services/AnalyticsService.ts
export class AnalyticsService {
  trackEvent(event: string, properties: Record<string, any>): void
  trackError(error: Error, context: string): void
  trackPerformance(metric: string, value: number): void
}
```

---

### 🔒 Security Considerations

#### **Data Protection**
- All sensitive data encrypted at rest
- RLS policies prevent unauthorized access
- Rate limiting on all endpoints
- Input validation and sanitization

#### **Anti-Cheat**
- Server-side score validation
- Reasonable bounds checking
- Anomaly detection for suspicious patterns
- Replay system for verification

---

### 🎯 Success Criteria

#### **Technical KPIs**
- 99.9% uptime for Supabase services
- <2s sync time for progress data
- <500ms leaderboard load time
- 0% data loss during sync conflicts

#### **Product KPIs**
- 70% of users create accounts within 7 days
- 85% of authenticated users use cloud sync
- 40% daily engagement with leaderboards
- 60% daily reward claim rate

---

### 📅 Timeline Summary

| Phase | Duration | Deliverables |
|-------|----------|-------------|
| Phase 1 | 2 weeks | Supabase setup, core services, feature flags |
| Phase 2 | 2 weeks | Authentication, progress sync, offline-first |
| Phase 3 | 2 weeks | Leaderboards, anti-cheat, social features |
| Phase 4 | 2 weeks | Daily rewards, inventory, spin wheel |
| **Total** | **8 weeks** | **Full cloud-enabled Match 3 game** |

---

### 🔄 Post-Launch Roadmap

#### **Phase 5: Advanced Features**
- Friends system & social sharing
- Tournaments & seasonal events  
- Achievement system
- Push notifications
- Advanced analytics dashboard

#### **Phase 6: Monetization**
- Premium subscription
- Power-up purchases
- Cosmetic items
- Battle pass system
