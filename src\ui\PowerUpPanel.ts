import { PowerUpType, PowerUpInventoryItem, PowerUpUtils } from '../types/PowerUpTypes';

/**
 * UI Panel cho power-ups
 */
export class PowerUpPanel {
    private scene: Phaser.Scene;
    private container: Phaser.GameObjects.Container;
    private powerUpButtons: Map<PowerUpType, PowerUpButton> = new Map();
    private isVisible: boolean = true;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.createPanel();
        this.setupEventListeners();
    }

    /**
     * Create power-up panel
     */
    private createPanel(): void {
        const { width, height } = this.scene.cameras.main;

        // Create container - positioned below game board, centered horizontally
        this.container = this.scene.add.container(width / 2, 705);
        this.container.setDepth(1000);

        // Create enhanced background với gradient effect (horizontal layout)
        const background = this.scene.add.rectangle(0, 0, 490, 120, 0x1a1a1a, 0.9);
        background.setStrokeStyle(3, 0x444444);
        this.container.add(background);

        // Add inner glow
        const innerGlow = this.scene.add.rectangle(0, 0, 484, 114, 0x333333, 0.3);
        innerGlow.setStrokeStyle(1, 0x666666);
        this.container.add(innerGlow);

        // Create animated title
        const title = this.scene.add.text(0, -45, 'POWER-UPS', {
            fontSize: '16px',
            color: '#ffff00',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        });
        title.setOrigin(0.5);
        this.container.add(title);

        // Add title glow animation
        this.scene.tweens.add({
            targets: title,
            alpha: { from: 0.8, to: 1 },
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Create power-up buttons
        this.createPowerUpButtons();

        // Add panel entrance animation (slide up from bottom)
        this.container.setY(height + 100);
        this.scene.tweens.add({
            targets: this.container,
            y: 705,
            duration: 500,
            ease: 'Back.easeOut'
        });
    }

    /**
     * Create buttons cho từng power-up
     */
    private createPowerUpButtons(): void {
        const powerUpTypes = [
            PowerUpType.HINT,
            PowerUpType.HAMMER,
            PowerUpType.BOMB,
            PowerUpType.LIGHTNING,
            PowerUpType.SHUFFLE,
            PowerUpType.COLOR_BLAST
        ];

        powerUpTypes.forEach((type, index) => {
            // Horizontal layout: 6 buttons with 80px spacing, centered
            const x = -200 + (index * 80);
            const button = new PowerUpButton(this.scene, x, 10, type);

            this.container.add(button.getContainer());
            this.powerUpButtons.set(type, button);
        });
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('power-up-inventory-updated', this.updateInventory, this);
        this.scene.events.on('power-up-mode-activated', this.onPowerUpActivated, this);
        this.scene.events.on('power-up-mode-deactivated', this.onPowerUpDeactivated, this);
        this.scene.events.on('power-up-processing-started', this.onProcessingStarted, this);
        this.scene.events.on('power-up-processing-finished', this.onProcessingFinished, this);
    }

    /**
     * Update inventory display
     */
    private updateInventory(inventory: PowerUpInventoryItem[]): void {
        inventory.forEach(item => {
            const button = this.powerUpButtons.get(item.type);
            if (button) {
                button.updateQuantity(item.quantity);
                // Use move-based cooldown if available, otherwise fallback to time-based
                const remainingMoves = PowerUpUtils.getRemainingMoveCooldown(item);
                const remainingTime = PowerUpUtils.getRemainingCooldown(item);
                button.updateCooldown(item.isOnCooldown, remainingMoves, remainingTime);
            }
        });
    }

    /**
     * Handle power-up activation
     */
    private onPowerUpActivated(type: PowerUpType): void {
        this.powerUpButtons.forEach((button, buttonType) => {
            if (buttonType === type) {
                button.setSelected(true);
            } else {
                button.setSelected(false);
            }
        });
    }

    /**
     * Handle power-up deactivation
     */
    private onPowerUpDeactivated(): void {
        this.powerUpButtons.forEach(button => {
            button.setSelected(false);
        });
    }

    /**
     * Handle power-up processing started
     */
    private onProcessingStarted(): void {
        // Disable all power-up buttons during processing
        this.powerUpButtons.forEach(button => {
            button.setDisabled(true);
        });
    }

    /**
     * Handle power-up processing finished
     */
    private onProcessingFinished(): void {
        // Re-enable all power-up buttons after processing
        this.powerUpButtons.forEach(button => {
            button.setDisabled(false);
        });
    }

    /**
     * Toggle panel visibility
     */
    public toggleVisibility(): void {
        this.isVisible = !this.isVisible;
        this.container.setVisible(this.isVisible);
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('power-up-inventory-updated', this.updateInventory, this);
        this.scene.events.off('power-up-mode-activated', this.onPowerUpActivated, this);
        this.scene.events.off('power-up-mode-deactivated', this.onPowerUpDeactivated, this);
        this.scene.events.off('power-up-processing-started', this.onProcessingStarted, this);
        this.scene.events.off('power-up-processing-finished', this.onProcessingFinished, this);

        this.powerUpButtons.forEach(button => button.destroy());
        this.powerUpButtons.clear();

        if (this.container) {
            this.container.destroy();
        }
    }
}

/**
 * Individual power-up button
 */
class PowerUpButton {
    private scene: Phaser.Scene;
    private container: Phaser.GameObjects.Container;
    private background: Phaser.GameObjects.Rectangle;
    private icon: Phaser.GameObjects.Sprite;
    private quantityText: Phaser.GameObjects.Text;
    private cooldownOverlay: Phaser.GameObjects.Rectangle;
    private cooldownText: Phaser.GameObjects.Text;
    private type: PowerUpType;
    private isSelected: boolean = false;

    constructor(scene: Phaser.Scene, x: number, y: number, type: PowerUpType) {
        this.scene = scene;
        this.type = type;
        this.createButton(x, y);
    }

    /**
     * Create button elements
     */
    private createButton(x: number, y: number): void {
        this.container = this.scene.add.container(x, y);

        // Enhanced background với gradient
        this.background = this.scene.add.rectangle(0, 0, 70, 70, 0x2a2a2a);
        this.background.setStrokeStyle(2, 0x555555);
        this.background.setInteractive();
        this.container.add(this.background);

        // Inner highlight
        const highlight = this.scene.add.rectangle(0, 0, 66, 66, 0x444444, 0.3);
        this.container.add(highlight);

        // Power-up specific icon với better visuals
        this.createPowerUpIcon();

        // Enhanced quantity display
        const quantityBg = this.scene.add.circle(25, 25, 12, 0x000000, 0.8);
        quantityBg.setStrokeStyle(1, 0xffffff);
        this.container.add(quantityBg);

        this.quantityText = this.scene.add.text(25, 25, '0', {
            fontSize: '14px',
            color: '#ffffff',
            fontStyle: 'bold'
        });
        this.quantityText.setOrigin(0.5);
        this.container.add(this.quantityText);

        // Enhanced cooldown overlay
        this.cooldownOverlay = this.scene.add.rectangle(0, 0, 70, 70, 0x000000, 0.8);
        this.cooldownOverlay.setVisible(false);
        this.container.add(this.cooldownOverlay);

        // Cooldown progress circle
        const cooldownCircle = this.scene.add.graphics();
        cooldownCircle.setVisible(false);
        this.container.add(cooldownCircle);

        this.cooldownText = this.scene.add.text(0, 0, '', {
            fontSize: '12px',
            color: '#ff4444',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        });
        this.cooldownText.setOrigin(0.5);
        this.cooldownText.setVisible(false);
        this.container.add(this.cooldownText);

        // Enhanced click handler với feedback
        this.background.on('pointerdown', () => {
            this.playClickAnimation();
            this.scene.events.emit('power-up-selected', this.type);
        });

        // Enhanced hover effects
        this.background.on('pointerover', () => {
            if (!this.isSelected) {
                this.background.setStrokeStyle(3, 0xaaaaaa);
                this.scene.tweens.add({
                    targets: this.container,
                    scaleX: 1.05,
                    scaleY: 1.05,
                    duration: 100,
                    ease: 'Power1.easeOut'
                });
            }
            this.showTooltip();
        });

        this.background.on('pointerout', () => {
            if (!this.isSelected) {
                this.background.setStrokeStyle(2, 0x555555);
                this.scene.tweens.add({
                    targets: this.container,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 100,
                    ease: 'Power1.easeOut'
                });
            }
            this.hideTooltip();
        });
    }

    /**
     * Update quantity display
     */
    public updateQuantity(quantity: number): void {
        this.quantityText.setText(quantity.toString());
        
        // Disable button if no quantity
        if (quantity <= 0) {
            this.icon.setAlpha(0.3);
            this.background.setFillStyle(0x222222);
            this.background.disableInteractive();
        } else {
            this.icon.setAlpha(1);
            this.background.setFillStyle(0x333333);
            this.background.setInteractive();
        }
    }

    /**
     * Update cooldown display (supports both move-based and time-based)
     */
    public updateCooldown(isOnCooldown: boolean, remainingMoves: number = 0, remainingTime: number = 0): void {
        if (isOnCooldown && (remainingMoves > 0 || remainingTime > 0)) {
            this.cooldownOverlay.setVisible(true);
            this.cooldownText.setVisible(true);

            // Prioritize move-based cooldown display
            if (remainingMoves > 0) {
                this.cooldownText.setText(remainingMoves.toString());
            } else {
                this.cooldownText.setText(Math.ceil(remainingTime / 1000).toString());
            }

            this.background.disableInteractive();
        } else {
            this.cooldownOverlay.setVisible(false);
            this.cooldownText.setVisible(false);
            if (parseInt(this.quantityText.text) > 0) {
                this.background.setInteractive();
            }
        }
    }

    /**
     * Set selected state
     */
    public setSelected(selected: boolean): void {
        this.isSelected = selected;
        if (selected) {
            this.background.setStrokeStyle(3, 0xffff00);
            this.background.setFillStyle(0x444444);
        } else {
            this.background.setStrokeStyle(2, 0x666666);
            this.background.setFillStyle(0x333333);
        }
    }

    /**
     * Set disabled state (when other power-up is processing)
     */
    public setDisabled(disabled: boolean): void {
        if (disabled) {
            // Dim the entire button
            this.container.setAlpha(0.4);
            this.background.disableInteractive();

            // Add visual overlay to indicate disabled state
            if (!this.container.getData('disabledOverlay')) {
                const overlay = this.scene.add.rectangle(0, 0, 70, 70, 0x000000, 0.6);
                this.container.add(overlay);
                this.container.setData('disabledOverlay', overlay);
            }
        } else {
            // Restore normal appearance
            this.container.setAlpha(1);

            // Remove disabled overlay
            const overlay = this.container.getData('disabledOverlay');
            if (overlay) {
                overlay.destroy();
                this.container.setData('disabledOverlay', null);
            }

            // Re-enable interaction if conditions are met
            const quantity = parseInt(this.quantityText.text);
            const isOnCooldown = this.cooldownOverlay.visible;
            if (quantity > 0 && !isOnCooldown) {
                this.background.setInteractive();
            }
        }
    }

    /**
     * Get container
     */
    public getContainer(): Phaser.GameObjects.Container {
        return this.container;
    }

    /**
     * Create power-up specific icon
     */
    private createPowerUpIcon(): void {
        // Get custom texture with atlas support
        const { texture, frame } = PowerUpUtils.getPowerUpTextureWithAtlas(this.scene, this.type);

        // Create icon với custom texture
        this.icon = this.scene.add.sprite(0, -5, texture, frame);
        this.icon.setScale(0.6); // Scale down to fit better in button

        this.container.add(this.icon);

        // Add subtle animation
        this.scene.tweens.add({
            targets: this.icon,
            scaleX: this.icon.scaleX * 1.1,
            scaleY: this.icon.scaleY * 1.1,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    /**
     * Play click animation
     */
    private playClickAnimation(): void {
        this.scene.tweens.add({
            targets: this.container,
            scaleX: 0.95,
            scaleY: 0.95,
            duration: 50,
            yoyo: true,
            ease: 'Power1.easeInOut'
        });
    }

    /**
     * Show tooltip
     */
    private showTooltip(): void {
        const properties = PowerUpUtils.getPowerUpProperties(this.type);

        // Create tooltip (simple implementation)
        const tooltip = this.scene.add.container(this.container.x - 150, this.container.y);
        tooltip.setDepth(2000);

        const tooltipBg = this.scene.add.rectangle(0, 0, 140, 60, 0x000000, 0.9);
        tooltipBg.setStrokeStyle(1, 0xffffff);
        tooltip.add(tooltipBg);

        const nameText = this.scene.add.text(0, -15, properties.name, {
            fontSize: '12px',
            color: '#ffff00',
            fontStyle: 'bold'
        });
        nameText.setOrigin(0.5);
        tooltip.add(nameText);

        const descText = this.scene.add.text(0, 5, properties.description, {
            fontSize: '10px',
            color: '#ffffff',
            wordWrap: { width: 130 }
        });
        descText.setOrigin(0.5);
        tooltip.add(descText);

        // Store reference for cleanup
        (this as any).tooltip = tooltip;
    }

    /**
     * Hide tooltip
     */
    private hideTooltip(): void {
        if ((this as any).tooltip) {
            (this as any).tooltip.destroy();
            (this as any).tooltip = null;
        }
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.hideTooltip();
        if (this.container) {
            this.container.destroy();
        }
    }
}
