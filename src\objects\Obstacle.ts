import { ObstacleType, ObstacleProperties, ObstacleTypeUtils } from '../types/ObstacleTypes';

/**
 * Class đại diện cho một chướng ngại vật trong game
 */
export class Obstacle extends Phaser.GameObjects.Sprite {
    public obstacleType: ObstacleType;
    public gridX: number;
    public gridY: number;
    public properties: ObstacleProperties;

    private glowSprite?: Phaser.GameObjects.Sprite;
    private glowTween?: Phaser.Tweens.Tween;

    constructor(scene: Phaser.Scene, x: number, y: number, obstacleType: ObstacleType, gridX: number, gridY: number) {
        // Get appropriate texture based on obstacle type
        const texture = ObstacleTypeUtils.getObstacleTexture(obstacleType);

        super(scene, x, y, texture);

        this.obstacleType = obstacleType;
        this.gridX = gridX;
        this.gridY = gridY;
        this.properties = ObstacleTypeUtils.getDefaultProperties(obstacleType);

        // Setup visual properties
        this.setDepth(1); // Same depth as gems
        this.setScale(0.6); // Match gem scale
        this.setOrigin(0.5, 0.5);

        // Add to scene
        scene.add.existing(this);

        // Setup interactive if destructible
        if (this.properties.isDestructible) {
            this.setupDestructibleVisuals();
        }
    }

    /**
     * Setup visual indicators for destructible obstacles
     */
    private setupDestructibleVisuals(): void {
        // Create a simple glow effect using the same texture with different tint
        this.glowSprite = this.scene.add.sprite(this.x, this.y, this.texture.key);
        this.glowSprite.setDepth(this.depth - 0.1);
        this.glowSprite.setScale(this.scaleX * 1.1);
        this.glowSprite.setAlpha(0.3);
        this.glowSprite.setTint(0x00ff88); // Green-cyan tint for destructible
        this.glowSprite.setOrigin(0.5, 0.5);

        // Animate glow
        this.glowTween = this.scene.tweens.add({
            targets: this.glowSprite,
            alpha: { from: 0.3, to: 0.6 },
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    /**
     * Take damage from special gem effects
     */
    public takeDamage(): boolean {
        if (!this.properties.isDestructible || !this.properties.durability) {
            return false;
        }

        // Reduce durability
        this.properties = ObstacleTypeUtils.reduceDurability(this.properties);

        // Play damage animation
        this.playDamageAnimation();

        // Check if destroyed
        if (this.properties.durability === 0) {
            // Start destroy animation but don't wait for it
            this.playDestroyAnimation().then(() => {
                // Animation completed, obstacle is fully destroyed
                console.log(`Obstacle ${this.obstacleType} at (${this.gridX}, ${this.gridY}) destruction animation completed`);
            });
            return true; // Obstacle is destroyed
        }

        // Update visual to show damage
        this.updateDamageVisual();
        return false; // Obstacle still exists
    }

    /**
     * Play damage animation
     */
    private playDamageAnimation(): void {
        // Flash red
        this.setTint(0xff0000);

        // Shake effect
        this.scene.tweens.add({
            targets: this,
            x: this.x + 5,
            duration: 50,
            yoyo: true,
            repeat: 3,
            ease: 'Power2.easeInOut',
            onComplete: () => {
                this.clearTint();
            }
        });
    }

    /**
     * Play destroy animation
     */
    private playDestroyAnimation(): Promise<void> {
        return new Promise((resolve) => {
            // Explosion effect
            this.scene.events.emit('obstacle-destroyed', {
                x: this.x,
                y: this.y,
                type: this.obstacleType
            });

            // Scale down and fade out
            this.scene.tweens.add({
                targets: this,
                scaleX: 0,
                scaleY: 0,
                alpha: 0,
                duration: 300,
                ease: 'Back.easeIn',
                onComplete: () => {
                    this.destroy();
                    resolve();
                }
            });
        });
    }

    /**
     * Update visual to show damage state
     */
    private updateDamageVisual(): void {
        if (!this.properties.durability) return;

        // Calculate damage percentage
        const maxDurability = ObstacleTypeUtils.getDefaultProperties(this.obstacleType).durability || 1;
        const damagePercent = 1 - (this.properties.durability / maxDurability);

        // Instead of overlay, use visual effects on the obstacle itself
        if (damagePercent > 0) {
            // Darken the obstacle to show damage
            const darkenAmount = 0.3 + (damagePercent * 0.4); // 0.3 to 0.7 darkness
            this.setTint(Phaser.Display.Color.GetColor32(
                Math.floor(255 * (1 - darkenAmount)),
                Math.floor(255 * (1 - darkenAmount)),
                Math.floor(255 * (1 - darkenAmount)),
                255
            ));

            // Add subtle red tint to indicate damage
            const redTint = Math.floor(255 * (0.8 + damagePercent * 0.2));
            const otherColors = Math.floor(255 * (1 - darkenAmount));
            this.setTint(Phaser.Display.Color.GetColor32(redTint, otherColors, otherColors, 255));

            // Add crack-like effect by slightly reducing scale
            const scaleReduction = damagePercent * 0.05; // Max 5% scale reduction
            this.setScale(0.6 - scaleReduction);
        }
    }

    /**
     * Check if this obstacle blocks movement
     */
    public blocksMovement(): boolean {
        return this.properties.blockMovement;
    }

    /**
     * Check if this obstacle blocks gravity
     */
    public blocksGravity(): boolean {
        return this.properties.blockGravity;
    }

    /**
     * Check if this obstacle can be destroyed
     */
    public isDestructible(): boolean {
        return this.properties.isDestructible && (this.properties.durability || 0) > 0;
    }

    /**
     * Get obstacle info for debugging
     */
    public getInfo(): string {
        return `Obstacle(${this.obstacleType}) at (${this.gridX},${this.gridY}) - Durability: ${this.properties.durability || 'Indestructible'}`;
    }

    /**
     * Cleanup when obstacle is destroyed
     */
    public destroy(): void {
        // Stop and remove glow tween
        if (this.glowTween) {
            this.glowTween.destroy();
            this.glowTween = undefined;
        }

        // Destroy glow sprite
        if (this.glowSprite) {
            this.glowSprite.destroy();
            this.glowSprite = undefined;
        }

        // Note: No need to cleanup damage overlay since we use tint effects now

        super.destroy();
    }
}