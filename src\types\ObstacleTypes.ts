/**
 * Enum cho các loại obstacle
 */
export enum ObstacleType {
    STONE = 'stone',           // <PERSON><PERSON> cơ bản - không thể phá hủy
    WOOD = 'wood',             // Gỗ - có thể phá hủy bằng special gems
    ICE = 'ice',               // Băng - có thể phá hủy, block gravity
    METAL = 'metal'            // Kim lo<PERSON> - rất khó phá hủy
}

/**
 * Interface cho obstacle properties
 */
export interface ObstacleProperties {
    isDestructible: boolean;    // C<PERSON> thể phá hủy không
    durability?: number;        // <PERSON><PERSON> bền (số lần hit để phá hủy)
    blockMovement: boolean;     // Block gem swapping
    blockGravity: boolean;      // Block gems falling through
    canBeAffectedBySpecial: boolean; // Có thể bị ảnh hưởng bởi special gems
}

/**
 * Interface cho obstacle position trong level config
 */
export interface ObstaclePosition {
    row: number;
    col: number;
    type: ObstacleType;
}

/**
 * Utility functions cho obstacle types
 */
export class ObstacleTypeUtils {
    /**
     * Get texture name cho obstacle type
     */
    static getObstacleTexture(obstacleType: ObstacleType): string {
        const textures = {
            [ObstacleType.STONE]: 'obstacle_stone',
            [ObstacleType.WOOD]: 'obstacle_wood', 
            [ObstacleType.ICE]: 'obstacle_ice',
            [ObstacleType.METAL]: 'obstacle_metal'
        };
        
        return textures[obstacleType] || 'obstacle_stone';
    }

    /**
     * Get default properties cho obstacle type
     */
    static getDefaultProperties(obstacleType: ObstacleType): ObstacleProperties {
        const properties = {
            [ObstacleType.STONE]: {
                isDestructible: false,
                blockMovement: true,
                blockGravity: true,
                canBeAffectedBySpecial: false
            },
            [ObstacleType.WOOD]: {
                isDestructible: true,
                durability: 2,
                blockMovement: true,
                blockGravity: true,
                canBeAffectedBySpecial: true
            },
            [ObstacleType.ICE]: {
                isDestructible: true,
                durability: 1,
                blockMovement: true,
                blockGravity: true,
                canBeAffectedBySpecial: true
            },
            [ObstacleType.METAL]: {
                isDestructible: true,
                durability: 3,
                blockMovement: true,
                blockGravity: true,
                canBeAffectedBySpecial: true
            }
        };

        return properties[obstacleType];
    }

    /**
     * Reduce durability của obstacle
     */
    static reduceDurability(properties: ObstacleProperties): ObstacleProperties {
        if (!properties.isDestructible || !properties.durability) {
            return properties;
        }

        return {
            ...properties,
            durability: Math.max(0, properties.durability - 1)
        };
    }

    /**
     * Check if obstacle type is destructible
     */
    static isDestructible(obstacleType: ObstacleType): boolean {
        return this.getDefaultProperties(obstacleType).isDestructible;
    }

    /**
     * Get obstacle color tint
     */
    static getObstacleColor(obstacleType: ObstacleType): number {
        const colors = {
            [ObstacleType.STONE]: 0x808080,  // Gray
            [ObstacleType.WOOD]: 0x8B4513,   // Brown
            [ObstacleType.ICE]: 0x87CEEB,    // Light blue
            [ObstacleType.METAL]: 0x708090   // Slate gray
        };
        
        return colors[obstacleType] || 0x808080;
    }
}
