export type StarThresholds = { one: number; two: number; three: number };

/**
 * StarRatingService
 * - Listens to score-updated and level-started
 * - Emits stars-updated with current star count and progress percent (0..100)
 */
export class StarRatingService {
  private scene: Phaser.Scene;
  private thresholds: StarThresholds = { one: 0, two: 0, three: 1 };
  private currentStars = 0;

  constructor(scene: Phaser.Scene) {
    this.scene = scene;
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.scene.events.on('level-started', this.onLevelStarted, this);
    this.scene.events.on('score-updated', this.onScoreUpdated, this);
  }

  private onLevelStarted(data: { starThresholds?: StarThresholds }): void {
    if (data && data.starThresholds) {
      this.thresholds = data.starThresholds;
    } else {
      // Fallback safe defaults
      this.thresholds = { one: 1000, two: 3000, three: 6000 };
    }
    // Reset stars to 0 at level start
    this.currentStars = 0;
    this.emitUpdate(0);
  }

  private onScoreUpdated(data: { score: number }): void {
    const score = data?.score ?? 0;
    const stars = this.calculateStars(score, this.thresholds);
    const changed = stars !== this.currentStars;
    this.currentStars = stars;
    this.emitUpdate(score, changed);
  }

  private calculateStars(score: number, t: StarThresholds): number {
    if (score >= t.three) return 3;
    if (score >= t.two) return 2;
    if (score >= t.one) return 1;
    return 0;
  }

  private emitUpdate(score: number, starChanged: boolean = false): void {
    const percent = Math.max(0, Math.min(100, (score / Math.max(1, this.thresholds.three)) * 100));
    this.scene.events.emit('stars-updated', {
      stars: this.currentStars,
      progressPercent: percent,
      thresholds: this.thresholds,
      score,
      starChanged
    });
  }

  public destroy(): void {
    this.scene.events.off('level-started', this.onLevelStarted, this);
    this.scene.events.off('score-updated', this.onScoreUpdated, this);
  }
}

