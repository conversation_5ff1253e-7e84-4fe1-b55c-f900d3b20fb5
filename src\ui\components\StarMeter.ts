export class StarMeter {
  private scene: Phaser.Scene;
  private container: Phaser.GameObjects.Container;
  private barBg: Phaser.GameObjects.Rectangle;
  private barFill: Phaser.GameObjects.Rectangle;
  private starIcons: Phaser.GameObjects.Image[] = [];

  constructor(scene: Phaser.Scene, x: number, y: number) {
    this.scene = scene;
    this.container = scene.add.container(x, y);

    // Background
    this.barBg = scene.add.rectangle(0, 0, 200, 16, 0x222222, 0.8).setOrigin(0, 0.5);
    this.barBg.setStrokeStyle(2, 0x555555);

    // Fill
    this.barFill = scene.add.rectangle(0, 0, 0, 12, 0xffd54f, 1).setOrigin(0, 0.5);

    // Stars with atlas support
    const starGap = 70;
    const { texture, frame } = this.getStarTextureWithAtlas();
    for (let i = 0; i < 3; i++) {
      const icon = scene.add.image(10 + i * starGap, -20, texture, frame);
      icon.setScale(0.22);
      icon.setAlpha(0.3);
      icon.setOrigin(0, 0.5);
      this.starIcons.push(icon);
    }

    this.container.add([this.barBg, this.barFill, ...this.starIcons]);
    this.container.setDepth(1001);
  }

  update(stars: number, percent: number): void {
    // Update fill width
    const clamped = Math.max(0, Math.min(100, percent));
    const targetWidth = (clamped / 100) * 200;

    this.scene.tweens.add({
      targets: this.barFill,
      width: targetWidth,
      duration: 300,
      ease: 'Sine.easeOut'
    });

    // Update stars alpha
    for (let i = 0; i < 3; i++) {
      const icon = this.starIcons[i];
      const shouldBeLit = i < stars;
      this.scene.tweens.add({
        targets: icon,
        alpha: shouldBeLit ? 1 : 0.3,
        scale: shouldBeLit ? 0.26 : 0.22,
        duration: 250,
        yoyo: true,
        ease: 'Back.easeOut'
      });
    }
  }

  animateStarEarned(targetIndex: number, startX: number, startY: number): void {
    const target = this.starIcons[targetIndex];
    if (!target) return;

    const { texture, frame } = this.getStarTextureWithAtlas();
    const temp = this.scene.add.image(startX, startY, texture, frame);
    temp.setScale(0.35);
    temp.setDepth(2000);

    // Convert target icon position to world space
    const worldPoint = new Phaser.Math.Vector2();
    this.container.getWorldTransformMatrix().transformPoint(target.x, target.y, worldPoint);

    this.scene.tweens.add({
      targets: temp,
      x: worldPoint.x,
      y: worldPoint.y,
      scale: 0.2,
      alpha: 0.9,
      duration: 600,
      ease: 'Cubic.easeInOut',
      onComplete: () => {
        temp.destroy();
        // Light-up effect
        target.setAlpha(1);
        this.scene.tweens.add({ targets: target, scale: 0.3, duration: 140, yoyo: true, ease: 'Back.easeOut' });
        // Subtle glow pulse on bar as well
        this.scene.tweens.add({ targets: this.barFill, alpha: { from: 1, to: 0.6 }, duration: 180, yoyo: true });
      }
    });
  }

  setVisible(visible: boolean): void {
    this.container.setVisible(visible);
  }

  setPosition(x: number, y: number): void {
    this.container.setPosition(x, y);
  }

  getContainer(): Phaser.GameObjects.Container {
    return this.container;
  }

  destroy(): void {
    this.container.destroy(true);
  }

  /**
   * Get star texture with atlas support
   */
  private getStarTextureWithAtlas(): { texture: string, frame?: string } {
    // Check if atlas is available
    const atlasAvailable = this.scene.registry.get('atlasAvailable') && this.scene.textures.exists('game_atlas');

    if (atlasAvailable) {
      return { texture: 'game_atlas', frame: 'star' };
    } else {
      return { texture: 'star' };
    }
  }
}

