/**
 * ObjectiveManager - <PERSON>õ<PERSON> tiến độ objectives và quyết định completion
 */

import { LevelObjective } from './LevelManager';
import { GemType } from '../types/GemTypes';

export interface ObjectiveProgress {
    type: 'collect' | 'destroy-stone-overlays';
    gemType?: string; // Optional for stone overlay objectives
    needed: number;
    collected: number;
    completed: boolean;
}

export interface GemTypeCount {
    [gemType: string]: number;
}

export class ObjectiveManager {
    private scene: Phaser.Scene;
    private objectives: LevelObjective[] = [];
    private progress: ObjectiveProgress[] = [];
    private allObjectivesCompleted: boolean = false;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.on('level-started', this.onLevelStarted, this);
        this.scene.events.on('stone-overlay-destroyed', this.onStoneOverlayDestroyed, this);
    }

    /**
     * Xử lý khi level started
     */
    private onLevelStarted(data: { objectives: LevelObjective[] }): void {
        this.initializeObjectives(data.objectives);
    }

    /**
     * Initialize objectives cho level mới
     */
    public initializeObjectives(objectives: LevelObjective[]): void {
        this.objectives = [...objectives];
        this.progress = objectives.map(objective => ({
            type: objective.type,
            gemType: objective.gemType, // Will be undefined for stone overlay objectives
            needed: objective.count,
            collected: 0,
            completed: false
        }));
        this.allObjectivesCompleted = false;

        // Emit initial progress
        this.emitProgressUpdate();
        
        console.log('Objectives initialized:', this.progress);
    }

    /**
     * Xử lý khi có matches được processed
     */
    private onMatchesProcessed(matchData: { matches: any[], isCombo: boolean }): void {
        const { matches } = matchData;

        console.log('ObjectiveManager: matches-processed event received', {
            matchCount: matches?.length || 0,
            isCombo: matchData.isCombo
        });

        if (!matches || matches.length === 0) {
            return;
        }

        // Count gems by type from matches
        const gemCounts = this.countGemsByType(matches);

        // Update progress
        this.updateProgressFromGemCounts(gemCounts);
    }

    /**
     * Xử lý khi stone overlay bị destroyed
     */
    private onStoneOverlayDestroyed(data: { gem: any }): void {
        console.log('ObjectiveManager: stone-overlay-destroyed event received');

        // Update stone overlay objectives
        this.updateStoneOverlayProgress();
    }

    /**
     * Update progress cho stone overlay objectives
     */
    private updateStoneOverlayProgress(): void {
        let hasUpdates = false;

        this.progress.forEach(objective => {
            if (objective.type === 'destroy-stone-overlays' && !objective.completed) {
                const previousCollected = objective.collected;
                objective.collected++;

                if (objective.collected >= objective.needed) {
                    objective.completed = true;
                }

                if (objective.collected !== previousCollected) {
                    hasUpdates = true;
                }
            }
        });

        // Emit updates if any
        if (hasUpdates) {
            console.log('Emitting stone overlay progress update');
            this.emitProgressUpdate();

            // Check if all objectives completed
            if (!this.allObjectivesCompleted && this.areAllObjectivesCompleted()) {
                this.allObjectivesCompleted = true;
                this.scene.events.emit('objective-completed');
                console.log('All objectives completed!');
            }
        }
    }

    /**
     * Count gems by type từ match data
     */
    private countGemsByType(matches: any[]): GemTypeCount {
        const counts: GemTypeCount = {};

        matches.forEach(gem => {
            if (gem && typeof gem.gemType === 'number') {
                // Convert GemType enum to string
                const gemTypeString = this.gemTypeEnumToString(gem.gemType);
                if (gemTypeString) {
                    counts[gemTypeString] = (counts[gemTypeString] || 0) + 1;
                }
            }
        });

        console.log('Gem counts:', counts); // Debug log
        return counts;
    }

    /**
     * Convert GemType enum to string
     */
    private gemTypeEnumToString(gemType: GemType): string | null {
        const mapping: { [key: number]: string } = {
            [GemType.RED]: 'red',
            [GemType.BLUE]: 'blue',
            [GemType.GREEN]: 'green',
            [GemType.YELLOW]: 'yellow',
            [GemType.PURPLE]: 'purple',
            [GemType.ORANGE]: 'orange',
            [GemType.WHITE]: 'white'
        };

        return mapping[gemType] || null;
    }

    /**
     * Update progress từ gem counts
     */
    private updateProgressFromGemCounts(gemCounts: GemTypeCount): void {
        let hasUpdates = false;
        let newlyCompleted: string[] = [];

        console.log('Updating progress from gem counts:', gemCounts);
        console.log('Current progress:', this.progress);

        this.progress.forEach(objective => {
            if (!objective.completed && gemCounts[objective.gemType]) {
                const previousCollected = objective.collected;
                objective.collected += gemCounts[objective.gemType];

                console.log(`Updated ${objective.gemType}: ${previousCollected} -> ${objective.collected}/${objective.needed}`);

                // Cap at needed amount
                if (objective.collected > objective.needed) {
                    objective.collected = objective.needed;
                }

                // Check if newly completed
                if (!objective.completed && objective.collected >= objective.needed) {
                    objective.completed = true;
                    newlyCompleted.push(objective.gemType);
                    console.log(`Objective completed: ${objective.gemType} (${objective.collected}/${objective.needed})`);
                }

                if (objective.collected !== previousCollected) {
                    hasUpdates = true;
                }
            }
        });

        // Emit updates if any
        if (hasUpdates) {
            console.log('Emitting progress update');
            this.emitProgressUpdate();

            // Check if all objectives completed
            if (!this.allObjectivesCompleted && this.areAllObjectivesCompleted()) {
                this.allObjectivesCompleted = true;
                this.scene.events.emit('objective-completed');
                console.log('All objectives completed!');
            }
        }

        // Emit individual objective completions
        newlyCompleted.forEach(gemType => {
            this.scene.events.emit('objective-gem-completed', { gemType });
        });
    }

    /**
     * Check if all objectives completed
     */
    private areAllObjectivesCompleted(): boolean {
        return this.progress.length > 0 && this.progress.every(objective => objective.completed);
    }

    /**
     * Emit progress update event
     */
    private emitProgressUpdate(): void {
        this.scene.events.emit('objective-updated', {
            progress: [...this.progress],
            allCompleted: this.areAllObjectivesCompleted()
        });
    }

    /**
     * Get current progress
     */
    public getProgress(): ObjectiveProgress[] {
        return [...this.progress];
    }

    /**
     * Get progress for specific gem type
     */
    public getProgressForGemType(gemType: string): ObjectiveProgress | null {
        return this.progress.find(p => p.gemType === gemType) || null;
    }

    /**
     * Check if specific objective completed
     */
    public isObjectiveCompleted(gemType: string): boolean {
        const objective = this.progress.find(p => p.gemType === gemType);
        return objective ? objective.completed : false;
    }

    /**
     * Check if all objectives completed
     */
    public isAllObjectivesCompleted(): boolean {
        return this.allObjectivesCompleted;
    }

    /**
     * Get completion percentage
     */
    public getCompletionPercentage(): number {
        if (this.progress.length === 0) return 0;
        
        const totalProgress = this.progress.reduce((sum, objective) => {
            return sum + (objective.collected / objective.needed);
        }, 0);
        
        return Math.min(100, (totalProgress / this.progress.length) * 100);
    }

    /**
     * Reset objectives
     */
    public reset(): void {
        this.objectives = [];
        this.progress = [];
        this.allObjectivesCompleted = false;
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.off('level-started', this.onLevelStarted, this);
        this.scene.events.off('stone-overlay-destroyed', this.onStoneOverlayDestroyed, this);
    }
}
