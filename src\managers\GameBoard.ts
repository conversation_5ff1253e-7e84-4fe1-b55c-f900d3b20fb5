import { Gem } from '../objects/Gem';
import { Obstacle } from '../objects/Obstacle';
import { ScoreManager } from './ScoreManager';
import { StoneOverlayManager } from './StoneOverlayManager';
import { StoneOverlayConfig } from './LevelManager';
import { GemType, SpecialGemType, MatchType, MatchInfo, GemTypeUtils } from '../types/GemTypes';
import { ObstacleType, ObstaclePosition } from '../types/ObstacleTypes';
import { PowerUpManager } from './PowerUpManager';
import { PowerUpActivationData } from '../types/PowerUpTypes';
import { PowerUpEffects } from '../effects/PowerUpEffects';
import { GemUnlockManager } from './GemUnlockManager';

/**
 * Class quản lý game board và logic chính
 */
export class GameBoard {
    private scene: Phaser.Scene;
    private grid: (Gem | Obstacle | null)[][];
    private boardWidth: number = 8;
    private boardHeight: number = 8;
    private boardShape: 'rectangle' | 'square' | 'hexagon' | 'triangle' = 'square';
    private cellSize: number = 60; // Reduced from 80 to 60
    private frameThickness: number = 8;
    private framePadding: number = 12;
    private startX: number;
    private startY: number;
    private selectedGem: Gem | null = null;
    private isProcessing: boolean = false;
    private scoreManager: ScoreManager;
    private stoneOverlayManager: StoneOverlayManager;
    private powerUpEffects: PowerUpEffects;
    private comboCount: number = 0;
    private gemUnlockManager: GemUnlockManager;
    private currentLevel: number = 1;

    // Visual elements that need cleanup
    private boardFrame?: Phaser.GameObjects.Sprite;
    private gridCells: Phaser.GameObjects.Sprite[] = [];
    private obstacles: Obstacle[] = [];
    
    constructor(scene: Phaser.Scene, scoreManager: ScoreManager, boardConfig?: { boardWidth: number; boardHeight: number; boardShape: 'rectangle' | 'square' | 'hexagon' | 'triangle'; obstacles?: ObstaclePosition[]; stoneOverlays?: StoneOverlayConfig }) {
        this.scene = scene;
        this.scoreManager = scoreManager;
        this.stoneOverlayManager = new StoneOverlayManager(scene);
        this.grid = [];
        this.powerUpEffects = new PowerUpEffects(scene);
        this.gemUnlockManager = GemUnlockManager.getInstance();

        // Set board dimensions from config or use defaults
        if (boardConfig) {
            this.boardWidth = boardConfig.boardWidth;
            this.boardHeight = boardConfig.boardHeight;
            this.boardShape = boardConfig.boardShape;
        }

        // Tính toán vị trí trung tâm với frame dựa trên board size
        this.calculateBoardPosition();

        this.initializeGrid();

        // Initialize obstacles if provided
        if (boardConfig?.obstacles) {
            this.initializeObstacles(boardConfig.obstacles);
        }

        // Initialize stone overlays if provided
        if (boardConfig?.stoneOverlays?.enabled) {
            this.initializeStoneOverlays(boardConfig.stoneOverlays);
        }

        this.setupEventListeners();
    }

    /**
     * Set current level for gem unlock system
     */
    public setCurrentLevel(level: number): void {
        this.currentLevel = level;
    }

    /**
     * Get available gem types for current level
     */
    private getAvailableGemTypes(): GemType[] {
        return this.gemUnlockManager.getAvailableGemsForLevel(this.currentLevel);
    }

    /**
     * Get random gem type from available gems
     */
    private getRandomGemType(): GemType {
        const availableGems = this.getAvailableGemTypes();
        const randomIndex = Phaser.Math.Between(0, availableGems.length - 1);
        return availableGems[randomIndex];
    }

    /**
     * Tính toán vị trí board dựa trên kích thước động
     */
    private calculateBoardPosition(): void {
        const totalBoardWidth = this.boardWidth * this.cellSize;
        const totalBoardHeight = this.boardHeight * this.cellSize;
        const totalFrameWidth = totalBoardWidth + (this.framePadding * 2) + (this.frameThickness * 2);
        const totalFrameHeight = totalBoardHeight + (this.framePadding * 2) + (this.frameThickness * 2);

        const frameStartX = (1024 - totalFrameWidth) / 2;
        // Move board down by 30px to create space between star meter and board
        const frameStartY = (768 - totalFrameHeight) / 2 + 30;
        this.startX = frameStartX + this.frameThickness + this.framePadding;
        this.startY = frameStartY + this.frameThickness + this.framePadding;
    }

    /**
     * Khởi tạo grid với gems ngẫu nhiên
     */
    private initializeGrid(): void {
        // Tạo board frame
        this.createBoardFrame();

        // Tạo background cells
        this.createGridBackground();

        // Khởi tạo array 2D
        for (let row = 0; row < this.boardHeight; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.boardWidth; col++) {
                this.grid[row][col] = null;
            }
        }

        // Fill với gems ngẫu nhiên (tránh matches ban đầu)
        this.fillGridWithoutMatches();
    }

    /**
     * Initialize obstacles từ level config
     */
    private initializeObstacles(obstaclePositions: ObstaclePosition[]): void {
        obstaclePositions.forEach(obstaclePos => {
            const { row, col, type } = obstaclePos;

            // Validate position
            if (row >= 0 && row < this.boardHeight && col >= 0 && col < this.boardWidth) {
                // Check if this position should have a cell
                if (this.shouldRenderCell(row, col)) {
                    // Remove any existing gem at this position
                    if (this.grid[row][col] instanceof Gem) {
                        (this.grid[row][col] as Gem).destroy();
                    }

                    // Calculate world position
                    const x = this.startX + col * this.cellSize + this.cellSize / 2;
                    const y = this.startY + row * this.cellSize + this.cellSize / 2;

                    // Create obstacle
                    const obstacle = new Obstacle(this.scene, x, y, type, col, row);
                    this.grid[row][col] = obstacle;
                    this.obstacles.push(obstacle);
                }
            }
        });

        console.log(`Initialized ${this.obstacles.length} obstacles`);
    }

    /**
     * Initialize stone overlays từ level config
     */
    private initializeStoneOverlays(stoneOverlayConfig: StoneOverlayConfig): void {
        if (!stoneOverlayConfig.enabled) {
            return;
        }

        let addedCount = 0;

        if (stoneOverlayConfig.specificPositions) {
            // Add stone overlays at specific positions
            addedCount = this.stoneOverlayManager.addStoneOverlaysAtPositions(
                this.grid as (Gem | null)[][],
                stoneOverlayConfig.specificPositions
            );
        } else if (stoneOverlayConfig.chance && stoneOverlayConfig.maxCount) {
            // Add random stone overlays based on chance và max count
            const targetCount = Math.min(
                stoneOverlayConfig.maxCount,
                Math.floor(this.boardWidth * this.boardHeight * stoneOverlayConfig.chance)
            );
            addedCount = this.stoneOverlayManager.addRandomStoneOverlays(
                this.grid as (Gem | null)[][],
                targetCount
            );
        }

        console.log(`Initialized ${addedCount} stone overlays`);
    }

    /**
     * Tạo board frame
     */
    private createBoardFrame(): void {
        const totalBoardWidth = this.boardWidth * this.cellSize;
        const totalBoardHeight = this.boardHeight * this.cellSize;
        const totalFrameWidth = totalBoardWidth + (this.framePadding * 2) + (this.frameThickness * 2);
        const totalFrameHeight = totalBoardHeight + (this.framePadding * 2) + (this.frameThickness * 2);
        const frameStartX = (1024 - totalFrameWidth) / 2;
        // Move board down by 30px to create space between star meter and board
        const frameStartY = (768 - totalFrameHeight) / 2 + 30;

        this.boardFrame = this.scene.add.sprite(
            frameStartX + totalFrameWidth / 2,
            frameStartY + totalFrameHeight / 2,
            'board_frame'
        );
        this.boardFrame.setDepth(-2);
    }

    /**
     * Tạo background cho grid
     */
    private createGridBackground(): void {
        // Clear existing grid cells
        this.gridCells.forEach(cell => cell.destroy());
        this.gridCells = [];

        for (let row = 0; row < this.boardHeight; row++) {
            for (let col = 0; col < this.boardWidth; col++) {
                // Check if this position should have a cell based on board shape
                if (this.shouldRenderCell(row, col)) {
                    const x = this.startX + col * this.cellSize + this.cellSize / 2;
                    const y = this.startY + row * this.cellSize + this.cellSize / 2;

                    const cell = this.scene.add.sprite(x, y, 'grid_cell');
                    cell.setDepth(0); // Above frame (-2) but below gems (1+)
                    this.gridCells.push(cell);
                }
            }
        }
    }

    /**
     * Xác định xem có nên render cell tại vị trí này không dựa trên board shape
     */
    private shouldRenderCell(row: number, col: number): boolean {
        switch (this.boardShape) {
            case 'rectangle':
            case 'square':
                // Rectangle và square render tất cả cells
                return true;

            case 'hexagon':
                // Hexagon shape - tạo hình lục giác bằng cách bỏ các góc
                const centerRow = Math.floor(this.boardHeight / 2);
                const centerCol = Math.floor(this.boardWidth / 2);
                const maxRadius = Math.min(centerRow, centerCol);

                // Tính khoảng cách Manhattan từ center
                const distanceFromCenter = Math.abs(row - centerRow) + Math.abs(col - centerCol);
                return distanceFromCenter <= maxRadius;

            case 'triangle':
                // Triangle shape - chỉ render cells tạo thành tam giác
                const maxColsInRow = Math.min(row + 1, this.boardWidth);
                const startCol = Math.floor((this.boardWidth - maxColsInRow) / 2);
                const endCol = startCol + maxColsInRow - 1;

                return col >= startCol && col <= endCol;

            default:
                return true;
        }
    }

    /**
     * Fill grid với gems mà không tạo matches ban đầu
     */
    private fillGridWithoutMatches(): void {
        const availableGems = this.getAvailableGemTypes();

        for (let row = 0; row < this.boardHeight; row++) {
            for (let col = 0; col < this.boardWidth; col++) {
                // Chỉ tạo gem nếu vị trí này có cell
                if (!this.shouldRenderCell(row, col)) {
                    continue;
                }

                let gemType: GemType;
                let attempts = 0;

                do {
                    const randomIndex = Phaser.Math.Between(0, availableGems.length - 1);
                    gemType = availableGems[randomIndex];
                    attempts++;
                } while (this.wouldCreateMatch(row, col, gemType) && attempts < 10);

                this.createGem(row, col, gemType);
            }
        }
    }
    
    /**
     * Kiểm tra xem việc đặt gem type tại vị trí có tạo match không
     */
    private wouldCreateMatch(row: number, col: number, gemType: GemType): boolean {
        // Kiểm tra horizontal
        let horizontalCount = 1;
        
        // Check left
        for (let c = col - 1; c >= 0; c--) {
            const cell = this.grid[row][c];
            if (cell instanceof Gem && cell.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }

        // Check right
        for (let c = col + 1; c < this.boardWidth; c++) {
            const cell = this.grid[row][c];
            if (cell instanceof Gem && cell.gemType === gemType) {
                horizontalCount++;
            } else {
                break;
            }
        }
        
        if (horizontalCount >= 3) return true;
        
        // Kiểm tra vertical
        let verticalCount = 1;
        
        // Check up
        for (let r = row - 1; r >= 0; r--) {
            const cell = this.grid[r][col];
            if (cell instanceof Gem && cell.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }

        // Check down
        for (let r = row + 1; r < this.boardHeight; r++) {
            const cell = this.grid[r][col];
            if (cell instanceof Gem && cell.gemType === gemType) {
                verticalCount++;
            } else {
                break;
            }
        }
        
        return verticalCount >= 3;
    }
    
    /**
     * Tạo gem tại vị trí grid
     */
    private createGem(row: number, col: number, gemType: GemType): void {
        const x = this.startX + col * this.cellSize + this.cellSize / 2;
        const y = this.startY + row * this.cellSize + this.cellSize / 2;

        const gem = new Gem(this.scene, x, y, gemType, col, row);
        this.grid[row][col] = gem;
    }
    
    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('gem-clicked', this.onGemClicked, this);
        this.scene.events.on('power-up-activated', this.onPowerUpActivated, this);
    }
    
    /**
     * Xử lý khi gem được click
     */
    private onGemClicked(gem: Gem): void {
        if (this.isProcessing) return;

        // Check if clicked gem is a special gem - activate immediately
        if (gem.isSpecial()) {
            this.activateSpecialGem(gem);
            return;
        }

        if (!this.selectedGem) {
            // Chọn gem đầu tiên
            this.selectedGem = gem;
            gem.select();
        } else if (this.selectedGem === gem) {
            // Bỏ chọn gem hiện tại
            gem.deselect();
            this.selectedGem = null;
        } else if (this.areAdjacent(this.selectedGem, gem) && this.canSwap(this.selectedGem, gem)) {
            // Swap hai gems liền kề (nếu không có obstacles blocking)
            this.attemptSwap(this.selectedGem, gem);
        } else {
            // Chọn gem mới
            this.selectedGem.deselect();
            this.selectedGem = gem;
            gem.select();
        }
    }

    /**
     * Handle power-up activation
     */
    private async onPowerUpActivated(data: PowerUpActivationData): Promise<void> {
        if (this.isProcessing) return;

        this.isProcessing = true;

        // Emit processing started event for UI feedback
        this.scene.events.emit('power-up-processing-started');

        try {
            // Clear any selected gem
            if (this.selectedGem) {
                this.selectedGem.deselect();
                this.selectedGem = null;
            }

            // Execute power-up effect
            const gemsToDestroy = await this.powerUpEffects.executePowerUpEffect(
                data,
                (gridX: number, gridY: number) => {
                    const cell = this.grid[gridY] && this.grid[gridY][gridX] ? this.grid[gridY][gridX] : null;
                    return cell instanceof Gem ? cell : null;
                },
                { rows: this.boardHeight, cols: this.boardWidth }
            );

            // Emit result để PowerUpManager biết power-up có thành công không
            const success = gemsToDestroy.length > 0 || data.type === 'shuffle' || data.type === 'hint';
            this.scene.events.emit('power-up-result', {
                success,
                gemsDestroyed: gemsToDestroy.length
            });

            // Chỉ xử lý tiếp nếu power-up thành công
            if (success) {
                // Handle obstacles affected by power-ups
                await this.handleObstaclesAffectedByPowerUp(data);

                // Handle special case for shuffle
                if (data.type === 'shuffle') {
                    await this.shuffleBoardGems();

                    // After shuffle, automatically detect and process any existing matches
                    await this.processMatchesAfterShuffle();
                } else if (gemsToDestroy.length > 0) {
                    // Emit matches processed event for scoring and objectives tracking
                    this.scene.events.emit('matches-processed', {
                        matches: gemsToDestroy,
                        isCombo: false,
                        isPowerUpActivation: true
                    });

                    // Remove destroyed gems
                    this.removeGemsFromGrid(gemsToDestroy);

                    // Apply gravity and refill
                    await this.applyGravity();
                    await this.refillGrid();

                    // Check for new matches
                    const matchInfos = this.findDetailedMatches();
                    if (matchInfos.length > 0) {
                        await this.processDetailedMatches(matchInfos, true);
                    }
                }
            }

        } catch (error) {
            console.error('Error processing power-up:', error);
            // Emit failure result
            this.scene.events.emit('power-up-result', {
                success: false,
                gemsDestroyed: 0
            });
        } finally {
            this.isProcessing = false;

            // Emit processing finished event for UI feedback
            this.scene.events.emit('power-up-processing-finished');
        }
    }

    /**
     * Kiểm tra hai gems có liền kề không
     */
    private areAdjacent(gem1: Gem, gem2: Gem): boolean {
        const dx = Math.abs(gem1.gridX - gem2.gridX);
        const dy = Math.abs(gem1.gridY - gem2.gridY);

        return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
    }

    /**
     * Activate special gem power
     */
    private async activateSpecialGem(gem: Gem): Promise<void> {
        if (this.isProcessing || !gem.isSpecial()) return;

        this.isProcessing = true;

        // Clear any selected gem
        if (this.selectedGem) {
            this.selectedGem.deselect();
            this.selectedGem = null;
        }

        // Activate the special gem
        gem.activateSpecialPower();

        // Get gems to destroy based on special gem type
        const gemsToDestroy = this.getGemsAffectedBySpecialPower(gem);

        if (gemsToDestroy.length > 0) {
            // Emit matches processed event for scoring
            this.scene.events.emit('matches-processed', {
                matches: gemsToDestroy,
                isCombo: false,
                isSpecialActivation: true
            });

            // Play special activation animation
            await this.playSpecialActivationAnimation(gem, gemsToDestroy);

            // Remove affected gems
            gemsToDestroy.forEach(affectedGem => {
                if (this.grid[affectedGem.gridY] && this.grid[affectedGem.gridY][affectedGem.gridX]) {
                    this.grid[affectedGem.gridY][affectedGem.gridX] = null;
                    affectedGem.destroy();
                }
            });

            // Apply gravity and refill
            await this.applyGravity();
            await this.refillGrid();

            // Check for new matches (cascade)
            const newMatchInfos = this.findDetailedMatches();
            if (newMatchInfos.length > 0) {
                await this.processDetailedMatches(newMatchInfos, true);
            } else {
                this.scene.events.emit('combo-broken');
            }
        }

        this.isProcessing = false;
    }

    /**
     * Get gems affected by special power activation
     */
    private getGemsAffectedBySpecialPower(specialGem: Gem): Gem[] {
        const affected: Gem[] = [];
        const { gridX, gridY } = specialGem;

        // Also track obstacles that should be damaged
        const obstaclesToDamage: Obstacle[] = [];

        switch (specialGem.specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
                // Destroy entire row
                for (let col = 0; col < this.boardWidth; col++) {
                    const cell = this.grid[gridY][col];
                    if (cell instanceof Gem && cell !== specialGem) {
                        affected.push(cell);
                    } else if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                        obstaclesToDamage.push(cell);
                    }
                }
                break;

            case SpecialGemType.STRIPED_VERTICAL:
                // Destroy entire column
                for (let row = 0; row < this.boardHeight; row++) {
                    const cell = this.grid[row][gridX];
                    if (cell instanceof Gem && cell !== specialGem) {
                        affected.push(cell);
                    } else if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                        obstaclesToDamage.push(cell);
                    }
                }
                break;

            case SpecialGemType.WRAPPED:
                // Destroy 3x3 area around the gem
                for (let row = Math.max(0, gridY - 1); row <= Math.min(this.boardHeight - 1, gridY + 1); row++) {
                    for (let col = Math.max(0, gridX - 1); col <= Math.min(this.boardWidth - 1, gridX + 1); col++) {
                        const cell = this.grid[row][col];
                        if (cell instanceof Gem && cell !== specialGem) {
                            affected.push(cell);
                        } else if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                            obstaclesToDamage.push(cell);
                        }
                    }
                }
                break;

            case SpecialGemType.COLOR_BOMB:
                // Destroy all gems of the same type as the special gem's base type
                const targetType = specialGem.gemType;
                for (let row = 0; row < this.boardHeight; row++) {
                    for (let col = 0; col < this.boardWidth; col++) {
                        const cell = this.grid[row][col];
                        if (cell instanceof Gem && cell !== specialGem && cell.gemType === targetType) {
                            affected.push(cell);
                        }
                    }
                }
                break;
        }

        // Process obstacle damage
        obstaclesToDamage.forEach(obstacle => {
            const isDestroyed = obstacle.takeDamage();
            if (isDestroyed) {
                // Remove obstacle from grid
                this.grid[obstacle.gridY][obstacle.gridX] = null;
                // Remove from obstacles array
                const index = this.obstacles.indexOf(obstacle);
                if (index > -1) {
                    this.obstacles.splice(index, 1);
                }
            }
        });

        // Always include the special gem itself
        affected.push(specialGem);

        return affected;
    }

    /**
     * Play special activation animation
     */
    private async playSpecialActivationAnimation(specialGem: Gem, affectedGems: Gem[]): Promise<void> {
        const animationPromises: Promise<void>[] = [];

        // Special gem explosion animation
        animationPromises.push(new Promise<void>((resolve) => {
            this.scene.tweens.add({
                targets: specialGem,
                scaleX: 1.5,
                scaleY: 1.5,
                alpha: 0,
                duration: 300,
                ease: 'Power2.easeOut',
                onComplete: () => resolve()
            });
        }));

        // Staggered destruction of affected gems
        affectedGems.forEach((gem, index) => {
            if (gem !== specialGem) {
                const delay = index * 50; // Stagger the animations

                animationPromises.push(new Promise<void>((resolve) => {
                    this.scene.time.delayedCall(delay, () => {
                        gem.playMatchAnimation().then(() => resolve());
                    });
                }));
            }
        });

        // Create special effect based on type
        this.createSpecialActivationEffect(specialGem);

        await Promise.all(animationPromises);
    }

    /**
     * Create visual effect for special gem activation
     */
    private createSpecialActivationEffect(specialGem: Gem): void {
        const { x, y } = specialGem;

        switch (specialGem.specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
                // Horizontal line effect
                this.scene.events.emit('create-special-effect', {
                    type: 'horizontal-line',
                    position: { x, y },
                    color: GemTypeUtils.getGemColor(specialGem.gemType)
                });
                break;

            case SpecialGemType.STRIPED_VERTICAL:
                // Vertical line effect
                this.scene.events.emit('create-special-effect', {
                    type: 'vertical-line',
                    position: { x, y },
                    color: GemTypeUtils.getGemColor(specialGem.gemType)
                });
                break;

            case SpecialGemType.WRAPPED:
                // Explosion effect
                this.scene.events.emit('create-special-effect', {
                    type: 'explosion',
                    position: { x, y },
                    color: GemTypeUtils.getGemColor(specialGem.gemType)
                });
                break;

            case SpecialGemType.COLOR_BOMB:
                // Rainbow explosion effect
                this.scene.events.emit('create-special-effect', {
                    type: 'color-bomb',
                    position: { x, y },
                    color: 0xffffff
                });
                break;
        }
    }

    /**
     * Thử swap hai gems
     */
    private async attemptSwap(gem1: Gem, gem2: Gem): Promise<void> {
        this.isProcessing = true;
        
        // Deselect gems
        gem1.deselect();
        this.selectedGem = null;
        
        // Swap positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate new world positions
        const gem1NewX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1NewY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2NewX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2NewY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate swap
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1NewX, gem1NewY),
            gem2.moveTo(tempCol, tempRow, gem2NewX, gem2NewY)
        ]);
        
        // Check for matches
        const matchInfos = this.findDetailedMatches();

        if (matchInfos.length > 0) {
            // Valid move - emit move event và process matches
            this.scene.events.emit('move-made');
            await this.processDetailedMatches(matchInfos, false);
        } else {
            // Invalid move - swap back
            await this.swapBack(gem1, gem2);
        }
        
        this.isProcessing = false;
    }
    
    /**
     * Swap back nếu move không hợp lệ
     */
    private async swapBack(gem1: Gem, gem2: Gem): Promise<void> {
        // Swap lại positions trong grid
        const tempRow = gem1.gridY;
        const tempCol = gem1.gridX;
        
        this.grid[gem1.gridY][gem1.gridX] = gem2;
        this.grid[gem2.gridY][gem2.gridX] = gem1;
        
        // Calculate original positions
        const gem1OrigX = this.startX + gem2.gridX * this.cellSize + this.cellSize / 2;
        const gem1OrigY = this.startY + gem2.gridY * this.cellSize + this.cellSize / 2;
        const gem2OrigX = this.startX + tempCol * this.cellSize + this.cellSize / 2;
        const gem2OrigY = this.startY + tempRow * this.cellSize + this.cellSize / 2;
        
        // Animate back
        await Promise.all([
            gem1.moveTo(gem2.gridX, gem2.gridY, gem1OrigX, gem1OrigY),
            gem2.moveTo(tempCol, tempRow, gem2OrigX, gem2OrigY)
        ]);
    }
    
    /**
     * Enhanced match detection với support cho special gems
     */
    private findMatches(): Gem[] {
        const matchInfos = this.findDetailedMatches();
        const allMatches: Set<Gem> = new Set();

        matchInfos.forEach(matchInfo => {
            matchInfo.gems.forEach(gem => allMatches.add(gem));
        });

        return Array.from(allMatches);
    }

    /**
     * Tìm detailed matches với thông tin về loại match
     */
    private findDetailedMatches(): MatchInfo[] {
        const matchInfos: MatchInfo[] = [];
        const processedGems: Set<Gem> = new Set();

        // Find horizontal matches
        for (let row = 0; row < this.boardHeight; row++) {
            let count = 0;
            let currentType: GemType | undefined = undefined;
            let startCol = -1;

            for (let col = 0; col <= this.boardWidth; col++) {
                const cell = col < this.boardWidth && this.shouldRenderCell(row, col) ? this.grid[row][col] : null;
                const gem = cell instanceof Gem ? cell : null;

                if (gem?.gemType === currentType && gem && !gem.isSpecial() && currentType !== undefined) {
                    count++;
                } else {
                    if (count >= 3 && currentType !== undefined) {
                        const matchGems: Gem[] = [];
                        for (let i = startCol; i < startCol + count; i++) {
                            if (this.shouldRenderCell(row, i)) {
                                const cell = this.grid[row][i];
                                if (cell instanceof Gem && !processedGems.has(cell)) {
                                    matchGems.push(cell);
                                    processedGems.add(cell);
                                }
                            }
                        }

                        if (matchGems.length >= 3) {
                            const matchType = this.determineMatchType(count);
                            const centerCol = Math.floor(startCol + count / 2);

                            matchInfos.push({
                                gems: matchGems,
                                type: matchType,
                                direction: 'horizontal',
                                centerPosition: { row, col: centerCol },
                                specialGemType: GemTypeUtils.determineSpecialGemType({
                                    gems: matchGems,
                                    type: matchType,
                                    direction: 'horizontal'
                                } as MatchInfo)
                            });
                        }
                    }

                    if (gem && !gem.isSpecial()) {
                        count = 1;
                        currentType = gem.gemType;
                        startCol = col;
                    } else {
                        count = 0;
                        currentType = undefined;
                        startCol = -1;
                    }
                }
            }
        }

        // Find vertical matches
        for (let col = 0; col < this.boardWidth; col++) {
            let count = 0;
            let currentType: GemType | undefined = undefined;
            let startRow = -1;

            for (let row = 0; row <= this.boardHeight; row++) {
                const cell = row < this.boardHeight && this.shouldRenderCell(row, col) ? this.grid[row][col] : null;
                const gem = cell instanceof Gem ? cell : null;

                if (gem?.gemType === currentType && gem && !gem.isSpecial() && currentType !== undefined) {
                    count++;
                } else {
                    if (count >= 3 && currentType !== undefined) {
                        const matchGems: Gem[] = [];
                        for (let i = startRow; i < startRow + count; i++) {
                            if (this.shouldRenderCell(i, col)) {
                                const cell = this.grid[i][col];
                                if (cell instanceof Gem && !processedGems.has(cell)) {
                                    matchGems.push(cell);
                                    processedGems.add(cell);
                                }
                            }
                        }

                        if (matchGems.length >= 3) {
                            const matchType = this.determineMatchType(count);
                            const centerRow = Math.floor(startRow + count / 2);

                            matchInfos.push({
                                gems: matchGems,
                                type: matchType,
                                direction: 'vertical',
                                centerPosition: { row: centerRow, col },
                                specialGemType: GemTypeUtils.determineSpecialGemType({
                                    gems: matchGems,
                                    type: matchType,
                                    direction: 'vertical'
                                } as MatchInfo)
                            });
                        }
                    }

                    if (gem && !gem.isSpecial()) {
                        count = 1;
                        currentType = gem.gemType;
                        startRow = row;
                    } else {
                        count = 0;
                        currentType = undefined;
                        startRow = -1;
                    }
                }
            }
        }

        // Find L and T shape matches
        const shapeMatches = this.findShapeMatches();
        matchInfos.push(...shapeMatches);

        return matchInfos;
    }

    /**
     * Find L and T shape matches
     */
    private findShapeMatches(): MatchInfo[] {
        const shapeMatches: MatchInfo[] = [];

        // Check each position for potential L/T shapes
        for (let row = 1; row < this.boardHeight - 1; row++) {
            for (let col = 1; col < this.boardWidth - 1; col++) {
                if (!this.shouldRenderCell(row, col)) continue;

                const centerCell = this.grid[row][col];
                if (!centerCell || !(centerCell instanceof Gem) || centerCell.isSpecial()) continue;

                const centerGem = centerCell;

                // Check for T shape (horizontal line + vertical center)
                const tShapeHorizontal = this.checkTShapeHorizontal(row, col);
                if (tShapeHorizontal) {
                    shapeMatches.push(tShapeHorizontal);
                }

                // Check for T shape (vertical line + horizontal center)
                const tShapeVertical = this.checkTShapeVertical(row, col);
                if (tShapeVertical) {
                    shapeMatches.push(tShapeVertical);
                }

                // Check for L shapes
                const lShapes = this.checkLShapes(row, col);
                shapeMatches.push(...lShapes);
            }
        }

        return shapeMatches;
    }

    /**
     * Check for T shape with horizontal base
     */
    private checkTShapeHorizontal(row: number, col: number): MatchInfo | null {
        const centerCell = this.grid[row][col];
        if (!centerCell || !(centerCell instanceof Gem)) return null;

        const centerGem = centerCell;

        // Check horizontal line (left-center-right)
        const leftCell = this.grid[row][col - 1];
        const rightCell = this.grid[row][col + 1];
        const left = leftCell instanceof Gem ? leftCell : null;
        const right = rightCell instanceof Gem ? rightCell : null;

        // Check vertical extensions (up or down)
        const upCell = this.grid[row - 1][col];
        const downCell = this.grid[row + 1][col];
        const up = upCell instanceof Gem ? upCell : null;
        const down = downCell instanceof Gem ? downCell : null;

        if (left?.gemType === centerGem.gemType && right?.gemType === centerGem.gemType) {
            if (up?.gemType === centerGem.gemType || down?.gemType === centerGem.gemType) {
                const gems = [centerGem, left, right];
                if (up?.gemType === centerGem.gemType) gems.push(up);
                if (down?.gemType === centerGem.gemType) gems.push(down);

                return {
                    gems,
                    type: MatchType.T_SHAPE,
                    centerPosition: { row, col },
                    specialGemType: SpecialGemType.WRAPPED
                };
            }
        }

        return null;
    }

    /**
     * Check for T shape with vertical base
     */
    private checkTShapeVertical(row: number, col: number): MatchInfo | null {
        const centerCell = this.grid[row][col];
        if (!centerCell || !(centerCell instanceof Gem)) return null;

        const centerGem = centerCell;

        // Check vertical line (up-center-down)
        const upCell = this.grid[row - 1][col];
        const downCell = this.grid[row + 1][col];
        const up = upCell instanceof Gem ? upCell : null;
        const down = downCell instanceof Gem ? downCell : null;

        // Check horizontal extensions (left or right)
        const leftCell = this.grid[row][col - 1];
        const rightCell = this.grid[row][col + 1];
        const left = leftCell instanceof Gem ? leftCell : null;
        const right = rightCell instanceof Gem ? rightCell : null;

        if (up?.gemType === centerGem.gemType && down?.gemType === centerGem.gemType) {
            if (left?.gemType === centerGem.gemType || right?.gemType === centerGem.gemType) {
                const gems = [centerGem, up, down];
                if (left?.gemType === centerGem.gemType) gems.push(left);
                if (right?.gemType === centerGem.gemType) gems.push(right);

                return {
                    gems,
                    type: MatchType.T_SHAPE,
                    centerPosition: { row, col },
                    specialGemType: SpecialGemType.WRAPPED
                };
            }
        }

        return null;
    }

    /**
     * Check for L shapes at position
     */
    private checkLShapes(row: number, col: number): MatchInfo[] {
        const lShapes: MatchInfo[] = [];
        const centerCell = this.grid[row][col];
        if (!centerCell || !(centerCell instanceof Gem)) return lShapes;

        const centerGem = centerCell;

        // Check all 4 L orientations
        const directions = [
            { h: [-1, 0], v: [0, 1] },  // Left-Down L
            { h: [1, 0], v: [0, 1] },   // Right-Down L
            { h: [-1, 0], v: [0, -1] }, // Left-Up L
            { h: [1, 0], v: [0, -1] }   // Right-Up L
        ];

        directions.forEach(dir => {
            const h1Cell = this.grid[row + dir.h[0]][col + dir.h[1]];
            const v1Cell = this.grid[row + dir.v[0]][col + dir.v[1]];
            const h1 = h1Cell instanceof Gem ? h1Cell : null;
            const v1 = v1Cell instanceof Gem ? v1Cell : null;

            if (h1?.gemType === centerGem.gemType && v1?.gemType === centerGem.gemType) {
                lShapes.push({
                    gems: [centerGem, h1, v1],
                    type: MatchType.L_SHAPE,
                    centerPosition: { row, col },
                    specialGemType: SpecialGemType.WRAPPED
                });
            }
        });

        return lShapes;
    }

    /**
     * Determine match type based on count
     */
    private determineMatchType(count: number): MatchType {
        if (count >= 5) {
            return MatchType.FIVE_PLUS;
        } else if (count === 4) {
            return MatchType.FOUR;
        } else {
            return MatchType.THREE;
        }
    }
    
    /**
     * Xử lý detailed matches với special gem creation và stone overlay support
     */
    private async processDetailedMatches(matchInfos: MatchInfo[], isCombo: boolean = false): Promise<void> {
        // Extract all gems from match infos
        const allMatches: Set<Gem> = new Set();
        matchInfos.forEach(matchInfo => {
            matchInfo.gems.forEach(gem => allMatches.add(gem));
        });

        // Update combo count
        if (isCombo) {
            this.comboCount++;
        } else {
            this.comboCount = 1; // First match in potential combo chain
        }

        // Process stone overlay gems và regular gems separately
        const { stoneOverlayGems, regularGems } = this.separateStoneOverlayGems(Array.from(allMatches));

        // Emit matches processed event for scoring (only regular gems count for score)
        this.scene.events.emit('matches-processed', { matches: regularGems, isCombo });

        // Emit combo event if combo count > 1
        if (this.comboCount > 1) {
            this.scene.events.emit('combo-achieved', {
                comboCount: this.comboCount,
                totalScore: this.scoreManager.getCurrentScore()
            });
        }

        // Handle stone overlay gems (remove overlay only)
        if (stoneOverlayGems.length > 0) {
            await this.processStoneOverlayGems(stoneOverlayGems);
        }

        // Handle regular gems (full destruction)
        if (regularGems.length > 0) {
            await this.processRegularGems(regularGems, matchInfos);
        }

        // Apply gravity và refill chỉ khi có gems bị destroy hoàn toàn
        if (regularGems.length > 0) {
            await this.applyGravity();
            await this.refillGrid();
        }

        // Check for new matches (cascade)
        const newMatchInfos = this.findDetailedMatches();
        if (newMatchInfos.length > 0) {
            await this.processDetailedMatches(newMatchInfos, true); // Cascade = combo
        } else {
            // No more matches - break combo
            this.comboCount = 0;
            this.scene.events.emit('combo-broken');
        }
    }

    /**
     * Separate gems into stone overlay gems và regular gems
     */
    private separateStoneOverlayGems(gems: Gem[]): { stoneOverlayGems: Gem[], regularGems: Gem[] } {
        const stoneOverlayGems: Gem[] = [];
        const regularGems: Gem[] = [];

        gems.forEach(gem => {
            if (gem.hasStoneOverlay()) {
                stoneOverlayGems.push(gem);
            } else {
                regularGems.push(gem);
            }
        });

        return { stoneOverlayGems, regularGems };
    }

    /**
     * Process stone overlay gems (remove overlay only)
     */
    private async processStoneOverlayGems(stoneOverlayGems: Gem[]): Promise<void> {
        // Play stone overlay removal animations
        await Promise.all(stoneOverlayGems.map(gem => gem.removeStoneOverlay()));

        console.log(`Removed stone overlays from ${stoneOverlayGems.length} gems`);
    }

    /**
     * Process regular gems (full destruction với special gem creation)
     */
    private async processRegularGems(regularGems: Gem[], matchInfos: MatchInfo[]): Promise<void> {
        // Play match animations
        await Promise.all(regularGems.map(gem => gem.playMatchAnimation()));

        // Filter matchInfos để chỉ include matches với regular gems
        const regularGemSet = new Set(regularGems);
        const filteredMatchInfos = matchInfos.map(matchInfo => ({
            ...matchInfo,
            gems: matchInfo.gems.filter(gem => regularGemSet.has(gem))
        })).filter(matchInfo => matchInfo.gems.length >= 3); // Chỉ keep matches với ít nhất 3 regular gems

        // Create special gems before removing matched gems
        const specialGemsToCreate = this.determineSpecialGemCreation(filteredMatchInfos);

        // Remove gems from grid
        regularGems.forEach(gem => {
            this.grid[gem.gridY][gem.gridX] = null;
            gem.destroy();
        });

        // Create special gems at appropriate positions
        await this.createSpecialGems(specialGemsToCreate);
    }

    /**
     * Determine which special gems to create from match infos
     */
    private determineSpecialGemCreation(matchInfos: MatchInfo[]): Array<{position: {row: number, col: number}, specialType: SpecialGemType, gemType: GemType}> {
        const specialGems: Array<{position: {row: number, col: number}, specialType: SpecialGemType, gemType: GemType}> = [];

        matchInfos.forEach(matchInfo => {
            if (matchInfo.specialGemType && matchInfo.specialGemType !== SpecialGemType.NONE && matchInfo.centerPosition) {
                // Get the gem type from the first gem in the match
                const gemType = matchInfo.gems[0].gemType;

                specialGems.push({
                    position: matchInfo.centerPosition,
                    specialType: matchInfo.specialGemType,
                    gemType: gemType
                });
            }
        });

        return specialGems;
    }

    /**
     * Create special gems at specified positions
     */
    private async createSpecialGems(specialGems: Array<{position: {row: number, col: number}, specialType: SpecialGemType, gemType: GemType}>): Promise<void> {
        const creationPromises: Promise<void>[] = [];

        specialGems.forEach(({ position, specialType, gemType }) => {
            const { row, col } = position;

            // Only create if position is empty
            if (!this.grid[row][col]) {
                const x = this.startX + col * this.cellSize + this.cellSize / 2;
                const y = this.startY + row * this.cellSize + this.cellSize / 2;

                const specialGem = new Gem(this.scene, x, y, gemType, col, row, specialType);
                this.grid[row][col] = specialGem;

                // Add creation animation
                specialGem.setScale(0);
                specialGem.setAlpha(0);

                const promise = new Promise<void>((resolve) => {
                    this.scene.tweens.add({
                        targets: specialGem,
                        scale: 0.45,
                        alpha: 1,
                        duration: 400,
                        ease: 'Back.easeOut',
                        onComplete: () => {
                            // Emit special gem created event
                            this.scene.events.emit('special-gem-created', {
                                gem: specialGem,
                                type: specialType,
                                position: { row, col }
                            });
                            resolve();
                        }
                    });
                });

                creationPromises.push(promise);
            }
        });

        await Promise.all(creationPromises);
    }
    
    /**
     * Áp dụng gravity - gems rơi xuống tự nhiên chỉ vào các vị trí hợp lệ
     */
    private async applyGravity(): Promise<void> {
        const movePromises: Promise<void>[] = [];

        for (let col = 0; col < this.boardWidth; col++) {
            let moved = true;

            // Lặp cho đến khi không còn gem nào có thể di chuyển xuống
            while (moved) {
                moved = false;

                // Duyệt từ dưới lên để tìm vị trí trống hợp lệ
                for (let row = this.boardHeight - 1; row >= 0; row--) {
                    // Nếu vị trí này trống và có thể chứa gem (không phải obstacle)
                    if (this.isEmpty(row, col) && this.canHaveGem(row, col)) {
                        // Tìm gem gần nhất phía trên để di chuyển xuống
                        for (let sourceRow = row - 1; sourceRow >= 0; sourceRow--) {
                            const cell = this.grid[sourceRow][col];

                            // Nếu gặp obstacle, dừng tìm kiếm (gems không thể rơi qua obstacles)
                            if (cell instanceof Obstacle) {
                                break;
                            }

                            // Nếu tìm thấy gem, di chuyển xuống
                            if (cell instanceof Gem) {
                                const gem = cell;

                                // Di chuyển gem xuống
                                this.grid[sourceRow][col] = null;
                                this.grid[row][col] = gem;

                                const newY = this.startY + row * this.cellSize + this.cellSize / 2;
                                movePromises.push(gem.moveTo(col, row, gem.x, newY));

                                moved = true;
                                break; // Chỉ di chuyển 1 gem mỗi lần
                            }
                        }
                    }
                }
            }
        }

        await Promise.all(movePromises);
    }
    
    /**
     * Refill grid với gems mới với staggered animation
     */
    private async refillGrid(): Promise<void> {
        const newGems: Promise<void>[] = [];
        let delay = 0;

        for (let col = 0; col < this.boardWidth; col++) {
            let gemsInColumn = 0;

            for (let row = 0; row < this.boardHeight; row++) {
                if (this.isEmpty(row, col) && this.canHaveGem(row, col)) {
                    const gemType = this.getRandomGemType();
                    const x = this.startX + col * this.cellSize + this.cellSize / 2;
                    const startY = this.startY - (gemsInColumn + 1) * this.cellSize;
                    const endY = this.startY + row * this.cellSize + this.cellSize / 2;

                    const gem = new Gem(this.scene, x, startY, gemType, col, row);
                    this.grid[row][col] = gem;

                    // Staggered timing for more natural feel
                    const gemDelay = delay + (gemsInColumn * 50);
                    const fallDuration = 300 + (gemsInColumn * 100);

                    newGems.push(new Promise<void>((resolve) => {
                        this.scene.time.delayedCall(gemDelay, () => {
                            gem.moveTo(col, row, x, endY, fallDuration).then(resolve);
                        });
                    }));

                    gemsInColumn++;
                }
            }

            delay += 30; // Slight delay between columns
        }

        await Promise.all(newGems);
    }









    /**
     * Reset board for new level
     */
    public resetBoard(): void {
        // Clear existing gems
        for (let row = 0; row < this.boardHeight; row++) {
            for (let col = 0; col < this.boardWidth; col++) {
                if (this.grid[row][col]) {
                    this.grid[row][col]!.destroy();
                    this.grid[row][col] = null;
                }
            }
        }

        // Reset state
        this.selectedGem = null;
        this.isProcessing = false;
        this.comboCount = 0;

        // Refill with new gems
        this.fillGridWithoutMatches();
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        // Remove event listeners
        this.scene.events.off('gem-clicked', this.onGemClicked, this);
        this.scene.events.off('power-up-activated', this.onPowerUpActivated, this);

        // Cleanup stone overlay manager
        if (this.stoneOverlayManager) {
            this.stoneOverlayManager.destroy();
        }

        // Destroy all gems and obstacles
        for (let row = 0; row < this.boardHeight; row++) {
            for (let col = 0; col < this.boardWidth; col++) {
                if (this.grid[row][col]) {
                    this.grid[row][col]!.destroy();
                }
            }
        }

        // Destroy all obstacles
        this.obstacles.forEach(obstacle => obstacle.destroy());
        this.obstacles = [];

        // Destroy board frame
        if (this.boardFrame) {
            this.boardFrame.destroy();
            this.boardFrame = undefined;
        }

        // Destroy grid cells
        this.gridCells.forEach(cell => cell.destroy());
        this.gridCells = [];
    }

    /**
     * Remove gems from grid (helper for power-ups)
     */
    private removeGemsFromGrid(gems: Gem[]): void {
        gems.forEach(gem => {
            if (this.grid[gem.gridY] && this.grid[gem.gridY][gem.gridX]) {
                this.grid[gem.gridY][gem.gridX] = null;
                gem.destroy();
            }
        });
    }

    /**
     * Shuffle board gems (for shuffle power-up)
     */
    private async shuffleBoardGems(): Promise<void> {
        // Collect all existing gems
        const allGems: Gem[] = [];
        const allPositions: { row: number, col: number }[] = [];

        for (let row = 0; row < this.boardHeight; row++) {
            for (let col = 0; col < this.boardWidth; col++) {
                const cell = this.grid[row][col];
                if (cell instanceof Gem && this.shouldRenderCell(row, col)) {
                    allGems.push(cell);
                    allPositions.push({ row, col });
                    this.grid[row][col] = null;
                }
            }
        }

        // Shuffle positions
        for (let i = allPositions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [allPositions[i], allPositions[j]] = [allPositions[j], allPositions[i]];
        }

        // Reassign gems to shuffled positions
        const movePromises: Promise<void>[] = [];

        allGems.forEach((gem, index) => {
            const newPos = allPositions[index];
            const newX = this.startX + newPos.col * this.cellSize + this.cellSize / 2;
            const newY = this.startY + newPos.row * this.cellSize + this.cellSize / 2;

            // Update grid position
            gem.gridX = newPos.col;
            gem.gridY = newPos.row;
            this.grid[newPos.row][newPos.col] = gem;

            // Animate to new position
            const promise = new Promise<void>((resolve) => {
                this.scene.tweens.add({
                    targets: gem,
                    x: newX,
                    y: newY,
                    duration: 600,
                    ease: 'Power2.easeInOut',
                    delay: index * 20, // Stagger animation
                    onComplete: () => resolve()
                });
            });

            movePromises.push(promise);
        });

        await Promise.all(movePromises);
    }

    /**
     * Process matches automatically after shuffle to ensure clean board state
     * This method processes matches without affecting combo count or scoring
     */
    private async processMatchesAfterShuffle(): Promise<void> {
        let hasMatches = true;

        // Keep processing matches until no more matches are found
        while (hasMatches) {
            const matchInfos = this.findDetailedMatches();

            if (matchInfos.length > 0) {
                // Process the matches silently (no scoring, no combo)
                await this.processMatchesSilently(matchInfos);

                // Small delay to allow animations to complete
                await new Promise(resolve => setTimeout(resolve, 100));
            } else {
                hasMatches = false;
            }
        }
    }

    /**
     * Process matches silently without affecting scoring or combo system
     * Used for cleaning up matches after shuffle
     */
    private async processMatchesSilently(matchInfos: MatchInfo[]): Promise<void> {
        // Extract all gems from match infos
        const allMatches: Set<Gem> = new Set();
        matchInfos.forEach(matchInfo => {
            matchInfo.gems.forEach(gem => allMatches.add(gem));
        });

        // Play match animations (but faster for cleanup)
        await Promise.all(Array.from(allMatches).map(gem => gem.playMatchAnimation()));

        // Remove gems from grid (no special gem creation for cleanup)
        allMatches.forEach(gem => {
            this.grid[gem.gridY][gem.gridX] = null;
            gem.destroy();
        });

        // Apply gravity
        await this.applyGravity();

        // Refill grid
        await this.refillGrid();
    }

    /**
     * Getter methods for board dimensions (for use in other classes)
     */
    public getCellSize(): number {
        return this.cellSize;
    }

    public getStartX(): number {
        return this.startX;
    }

    public getStartY(): number {
        return this.startY;
    }

    public getBoardWidth(): number {
        return this.boardWidth;
    }

    public getBoardHeight(): number {
        return this.boardHeight;
    }

    public getBoardShape(): 'rectangle' | 'square' | 'hexagon' | 'triangle' {
        return this.boardShape;
    }

    /**
     * Helper methods cho obstacles
     */

    /**
     * Check if position có obstacle
     */
    private hasObstacle(row: number, col: number): boolean {
        const cell = this.grid[row]?.[col];
        return cell instanceof Obstacle;
    }

    /**
     * Check if position có gem
     */
    private hasGem(row: number, col: number): boolean {
        const cell = this.grid[row]?.[col];
        return cell instanceof Gem;
    }

    /**
     * Get obstacle at position
     */
    private getObstacle(row: number, col: number): Obstacle | null {
        const cell = this.grid[row]?.[col];
        return cell instanceof Obstacle ? cell : null;
    }

    /**
     * Get gem at position (public for PowerUpManager validation)
     */
    public getGem(row: number, col: number): Gem | null {
        const cell = this.grid[row]?.[col];
        return cell instanceof Gem ? cell : null;
    }

    /**
     * Get grid reference (public for PowerUpManager validation)
     */
    public getGrid(): (Gem | Obstacle | null)[][] {
        return this.grid;
    }

    /**
     * Check if position is empty (no gem, no obstacle)
     */
    private isEmpty(row: number, col: number): boolean {
        return this.grid[row]?.[col] === null;
    }

    /**
     * Check if position can have gems (not obstacle, valid cell)
     */
    private canHaveGem(row: number, col: number): boolean {
        return this.shouldRenderCell(row, col) && !this.hasObstacle(row, col);
    }

    /**
     * Check if two gems can be swapped (no obstacles blocking)
     */
    private canSwap(gem1: Gem, gem2: Gem): boolean {
        // Both positions must be valid gems (not obstacles)
        return this.hasGem(gem1.gridY, gem1.gridX) && this.hasGem(gem2.gridY, gem2.gridX);
    }

    /**
     * Handle obstacles affected by power-up activation
     */
    private async handleObstaclesAffectedByPowerUp(data: PowerUpActivationData): Promise<void> {
        if (!data.position || data.position.gridX === undefined || data.position.gridY === undefined) return;

        const { gridX, gridY } = data.position;
        const obstaclesToDamage: Obstacle[] = [];

        // Determine which obstacles are affected based on power-up type
        switch (data.type) {
            case 'bomb':
                // 5x5 area around activation point
                const radius = 2;
                for (let row = gridY - radius; row <= gridY + radius; row++) {
                    for (let col = gridX - radius; col <= gridX + radius; col++) {
                        if (row >= 0 && row < this.boardHeight && col >= 0 && col < this.boardWidth) {
                            const cell = this.grid[row][col];
                            if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                                obstaclesToDamage.push(cell);
                            }
                        }
                    }
                }
                break;

            case 'lightning':
                // Entire row and column
                for (let col = 0; col < this.boardWidth; col++) {
                    const cell = this.grid[gridY][col];
                    if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                        obstaclesToDamage.push(cell);
                    }
                }
                for (let row = 0; row < this.boardHeight; row++) {
                    const cell = this.grid[row][gridX];
                    if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial && !obstaclesToDamage.includes(cell)) {
                        obstaclesToDamage.push(cell);
                    }
                }
                break;

            case 'hammer':
                // Single position
                const cell = this.grid[gridY][gridX];
                if (cell instanceof Obstacle && cell.properties.canBeAffectedBySpecial) {
                    obstaclesToDamage.push(cell);
                }
                break;

            case 'color_blast':
                // Color blast doesn't affect obstacles
                break;

            case 'shuffle':
            case 'hint':
                // These don't damage obstacles
                break;
        }

        // Process obstacle damage
        for (const obstacle of obstaclesToDamage) {
            const isDestroyed = obstacle.takeDamage();
            if (isDestroyed) {
                // Remove obstacle from grid
                this.grid[obstacle.gridY][obstacle.gridX] = null;

                // Remove from obstacles array
                const index = this.obstacles.indexOf(obstacle);
                if (index > -1) {
                    this.obstacles.splice(index, 1);
                }

                console.log(`Obstacle destroyed at (${obstacle.gridX}, ${obstacle.gridY})`);
            }
        }
    }

    /**
     * Get StoneOverlayManager instance
     */
    public getStoneOverlayManager(): StoneOverlayManager {
        return this.stoneOverlayManager;
    }

}
