# Kế hoạch refactor gameplay: từ endless scoring sang level-based objectives

## 1) <PERSON><PERSON><PERSON> ti<PERSON> & Phạm vi
- Chuyển core loop từ "đạt điểm mục tiêu" sang "hoàn thành objective theo loại gem" trong giới hạn lư<PERSON>uyển (Move Limit).
- <PERSON><PERSON><PERSON> hệ thống điểm để tính hạng sao (1–3 sao), không ảnh hưởng pass/fail level.
- Thêm hệ thống Level Progression: cấu hình level, màn chọn level, lưu tiến trình (sao đạt được, level mở khóa).

## 2) Ảnh hưởng đến kiến trúc hiện tại
Kiến trúc hiện có (rút gọn):
- Scenes: <PERSON><PERSON>, Preloader, MainMenu, Game, GameOver
- Core: GameBoard (logic grid, swap, match, gravity, refill), Gem
- Managers: ScoreManager (điểm + combo + targetScore để complete), <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Up<PERSON>anager, PowerUpCollectionSystem
- Events: GameBoard phát `move-made`, `matches-processed`; ScoreManager nghe events để tính điểm, giảm movesRemaining, check targetScore.

Khoảng trống so với yêu cầu mới:
- Completion logic đang dựa vào `targetScore` trong ScoreManager → cần tách ra thành Objective-based.
- Chưa có nơi quản lý level data (objectives, move limit, star thresholds) và progression.
- UI chưa hiển thị objective progress và star meter theo score.

## 3) Định hướng kiến trúc mục tiêu
Tách bạch các concerns, theo hướng event-driven, tránh coupling vào ScoreManager.

Thành phần mới:
1) LevelManager (mới)
- Nhiệm vụ: Load level config, cung cấp runtime state (moveLimit, objectives, star thresholds), phát sự kiện level start/end, lưu kết quả.
- API chính: `loadLevel(levelId)`, `getObjectives()`, `getMoveLimit()`, `getStarThresholds()`, `consumeMove()`, `isObjectiveCompleted()`, `onLevelComplete(result)`.

2) ObjectiveManager (mới)
- Nhiệm vụ: Theo dõi tiến độ objective (đếm số gems bị phá theo loại), quyết định complete objective.
- Event inputs: `matches-processed` (từ GameBoard) hoặc một event mới `gems-cleared` (nếu bổ sung), `special-activated` (để cộng thêm khi power tác động nhiều ô).
- API/Events: `updateProgressFromMatches(matchInfos)`, `getProgress()`, emit `objective-updated`, `objective-completed`.

3) StarRatingService (mới)
- Nhiệm vụ: Tính số sao dựa trên điểm hiện tại và thresholds của level.
- API/Events: `recalculate(score) -> {stars, progressPercent}`, emit `stars-updated`.

4) ProgressService (mới)
- Nhiệm vụ: Lưu/đọc tiến trình (localStorage): level unlocked, best stars, best score.
- API: `getProgress()`, `setLevelResult(levelId, stars, score)`, `isUnlocked(levelId)`.

5) LevelSelectScene (mới)
- Grid hiển thị level, trạng thái unlocked/locked, số sao đạt tốt nhất.

6) HUD/UIScene mở rộng (modify UIManager)
- Thêm panel Objectives (list yêu cầu + tiến độ), Moves Left, Star Meter, Level title.
- End-of-level modal: Victory/Defeat, sao đạt được, nút Retry/Next/Level Select.

Điều chỉnh thành phần hiện có:
- ScoreManager: Giữ vai trò tính điểm + combo; BỎ logic complete theo `targetScore`. Chỉ emit `score-updated`.
- GameScene: Khởi tạo LevelManager, ObjectiveManager, StarRatingService, kết nối events; kết thúc màn dựa vào ObjectiveManager + LevelManager (hết moves → thua nếu chưa xong objectives).
- GameBoard: Không cần thay đổi lớn. Tiếp tục emit `matches-processed`. Nếu cần rõ ràng hơn, ta bổ sung emit `gems-cleared` kèm thống kê theo GemType ngay sau khi remove.

## 4) Dòng sự kiện (Event Flow) đề xuất
- Player swap hợp lệ → GameBoard emit `move-made`
- LevelManager.consumeMove() giảm moves; UI update; nếu moves==0 và ObjectiveManager chưa completed → defeat
- GameBoard process matches → emit `matches-processed` với chi tiết nhóm match (đang có)
- ScoreManager nhận `matches-processed` → tính điểm → emit `score-updated`
- ObjectiveManager nhận `matches-processed` → cộng tiến độ theo GemType → emit `objective-updated`; nếu đạt hết → emit `objective-completed`
- StarRatingService nhận `score-updated` → tính số sao hiện thời → emit `stars-updated`
- Khi `objective-completed`: LevelManager đánh dấu victory, mở modal kết quả, gọi ProgressService lưu best stars/score, cho phép Next

## 5) Cấu trúc dữ liệu Level Configuration
File: assets/data/levels.json
Schema (đề xuất, có thể mở rộng về sau):
- id: number
- name: string
- board: { gridSize?: number, allowedGemTypes?: string[] }
- objectives: Array<{ type: 'collect'; gemType: 'red'|'blue'|'green'|'yellow'|'purple'|'orange'|'white'; count: number }>
- moveLimit: number
- starThresholds: { one: number; two: number; three: number }

Ví dụ (Level 1):
{
  "levels": [
    {
      "id": 1,
      "name": "Getting Started",
      "board": { "gridSize": 8 },
      "objectives": [
        { "type": "collect", "gemType": "red", "count": 20 },
        { "type": "collect", "gemType": "blue", "count": 15 }
      ],
      "moveLimit": 20,
      "starThresholds": { "one": 2000, "two": 5000, "three": 9000 }
    }
  ]
}

Lưu ý:
- Tách mock data (levels.json) khỏi source code. Không hard-code trong TS.
- Có thể thêm loại objective khác trong tương lai (ví dụ: phá special gems, clear blockers) mà không phá vỡ cấu trúc tổng thể.

## 6) Thay đổi file/class (Create/Modify)
Create:
- src/managers/LevelManager.ts
- src/managers/ObjectiveManager.ts
- src/services/StarRatingService.ts (hoặc đặt trong managers)
- src/services/ProgressService.ts
- src/scenes/LevelSelect.ts
- assets/data/levels.json (mock data cho 5–20 level mẫu)
- src/ui/components/ObjectivesPanel.ts (nếu tách khỏi UIManager)
- src/ui/components/StarMeter.ts
- src/ui/EndLevelModal.ts

Modify:
- src/scenes/Preloader.ts: load assets/data/levels.json
- src/scenes/Game.ts: khởi tạo LevelManager, ObjectiveManager, StarRatingService; nối event wiring; handle end states; truyền levelId khi start
- src/managers/ScoreManager.ts: bỏ `targetScore` completion logic; vẫn giữ combo và highscore; chỉ emit `score-updated`
- src/managers/UIManager.ts: thêm UI elements: Objectives list, MovesLeft, StarMeter, LevelTitle; react theo events `objective-updated`, `moves-updated`, `stars-updated`
- src/managers/GameBoard.ts: (tùy chọn) emit thêm `gems-cleared` chứa map GemType→count ngay sau bước remove, để ObjectiveManager dùng trực tiếp (nếu không, ObjectiveManager tự tổng hợp từ `matches-processed`)
- src/game/main.ts và MainMenu: thêm luồng để đi vào LevelSelectScene thay vì Play ngay

## 7) Logic chi tiết theo hệ thống
A) Objective-based Completion
- ObjectiveManager giữ state dạng: { red: {need:20, have: x}, ... }
- Mỗi lần `matches-processed`, gom nhóm theo GemType và tăng `have` tương ứng.
- Khi mọi mục tiêu đạt `have >= need`, emit `objective-completed`.

B) Move Limit
- LevelManager khởi tạo `movesRemaining = moveLimit` từ config.
- Lắng nghe `move-made` để `consumeMove()`; emit `moves-updated`.
- Nếu movesRemaining == 0 và chưa complete objectives → defeat.

C) Star Rating (1–3 sao) theo điểm
- StarRatingService lắng nghe `score-updated` từ ScoreManager.
- So điểm hiện tại với thresholds để tính số sao và % progress cho StarMeter.
- Không ảnh hưởng pass/fail; chỉ hiển thị và dùng lưu thành tích tốt nhất.

D) Level Progression
- LevelSelectScene đọc ProgressService để hiển thị unlocked/locked, best stars.
- Khi thắng level: ProgressService cập nhật best stars/score; unlock level tiếp theo.

## 8) Kế hoạch UI/UX
- HUD trái/phải: 
  - Objectives Panel: list item (icon gem + "x/target") kèm tint/animation khi hoàn thành từng mục.
  - Moves Left: badge lớn, cảnh báo khi <5.
  - Star Meter: thanh 3 mốc (1/2/3 sao) + fill theo % điểm.
  - Level Title: tên level hiện tại.
- End-of-level Modal:
  - Victory: số sao đạt, điểm, buttons: Retry, Next Level, Level Select.
  - Defeat: lý do (hết lượt), gợi ý Retry, Level Select.
- Level Select Scene:
  - Grid button levels (id + stars), locked có padlock, unlocked có preview.
- Mobile responsive: đảm bảo panels không che board; dùng Container và anchors linh hoạt.

## 9) Phân phase triển khai
Phase 1 – Foundation (objective/moves tách score)
- Tạo LevelManager, ObjectiveManager (state + wiring events)
- Bỏ targetScore completion khỏi ScoreManager
- UI: MovesLeft + Objectives Panel cơ bản
- Điều kiện kết thúc: objective completed = thắng; hết moves = thua

Phase 2 – Star Rating
- Thêm StarRatingService, StarMeter UI
- Hooks vào `score-updated`

Phase 3 – Level Data & Loading
- Tạo assets/data/levels.json + loader ở Preloader
- LevelManager: load theo levelId, apply moveLimit/objectives/star thresholds
- GameScene nhận levelId khi start

Phase 4 – Cloud Progression with Supabase & Level Select
- Tích hợp Supabase (auth + database bằng @supabase/supabase-js; bật qua feature flag; offline-first fallback)
- Tài khoản/Auth: Email OTP/OAuth; bảng profiles (id, username, display_name)
- Cloud save & sync: bảng player_progress (user_id, level_id, best_stars, best_score, unlocked, updated_at); merge từ local khi login
- Leaderboards: bảng leaderboards theo level (public read, user write, chống spam qua RLS/Edge Functions)
- Tính năng online mở rộng:
  - Nhận quà hàng ngày (server-validated timestamp), thưởng: power-up items
  - Vòng quay nhận power-up items (Edge Function/RPC), ghi vào inventory
  - Inventory: bảng player_items (user_id, item_type, quantity, updated_at)
- LevelSelectScene: fetch tiến trình từ Supabase; hiển thị lock/stars; fallback offline dùng local; auto-sync khi online
- EndLevel flow: upsert kết quả lên Supabase, mở khóa level tiếp theo, (optional) push leaderboard entry

Phase 5 – Polish & QA
- Hiệu ứng UI/particles khi hoàn thành từng objective
- Animation sao bay khi đạt mốc
- Unit tests cho managers; integration test flow thắng/thua

## 10) Event contract & tích hợp tối thiểu
- Giữ nguyên `matches-processed` hiện có để tránh thay đổi lớn trong GameBoard.
- ObjectiveManager sẽ tính GemType counts từ matchInfos hiện hành.
- Optional (nếu cần tối ưu): bổ sung `gems-cleared: { [gemType]: number }` tại GameBoard sau khi remove.

## 11) Lưu trữ tiến trình (ProgressService)
- Lưu trong localStorage key: `match3_progress_v1`
- Dữ liệu: { levels: { [id]: { bestStars: number, bestScore: number, unlocked: boolean } } }
- Khởi tạo: level 1 unlocked mặc định.

## 12) Rủi ro & Giảm thiểu
- Coupling lịch sử giữa ScoreManager và completion: đã tách khỏi targetScore.
- Đồng bộ events: đảm bảo thứ tự xử lý `move-made` → `matches-processed` → update score/objective, và check end states sau mỗi cascade.
- UI quá tải: kiểm tra responsive, hạn chế che board.

## 13) Kiểm thử & Cách chạy test
- Unit tests: ObjectiveManager (cập nhật tiến độ), LevelManager (consumeMove, end conditions), StarRatingService (threshold logic), ProgressService (read/write/localStorage mocks).
- Integration: mô phỏng chuỗi events từ GameBoard để xác nhận win/lose, star calc.
- Manual QA: các case edge (đạt objective trong lượt cuối; đạt 3 sao nhưng chưa đạt objective; cascade nhiều lần).

## 14) Checklist thay đổi file
- New: LevelManager.ts, ObjectiveManager.ts, StarRatingService.ts, ProgressService.ts, LevelSelect.ts, ObjectivesPanel.ts, StarMeter.ts, EndLevelModal.ts, assets/data/levels.json
- Modify: Preloader.ts (load JSON), Game.ts (wire managers, end flow), UIManager.ts (hiển thị UI mới), ScoreManager.ts (remove targetScore completion), GameBoard.ts (optional `gems-cleared`)

## 15) Triển khai dần & Clean code
- Giữ code sạch: tách UI components, đặt tên English cho FE/BE code.
- Không hard-code số liệu; tất cả lấy từ level config.
- Giữ mock data tách biệt (assets/data/levels.json).
- Áp dụng best practices về event-driven và single responsibility cho managers.

