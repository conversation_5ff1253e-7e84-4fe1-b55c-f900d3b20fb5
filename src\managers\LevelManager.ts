/**
 * LevelManager - Quản lý level configuration, move limits và level state
 */

import { ObstaclePosition } from '../types/ObstacleTypes';

export interface LevelObjective {
    type: 'collect' | 'destroy-stone-overlays';
    gemType?: 'red' | 'blue' | 'green' | 'yellow' | 'purple' | 'orange' | 'white'; // Optional for stone overlay objectives
    count: number;
}

export interface StoneOverlayConfig {
    enabled: boolean;
    chance?: number; // 0-1, probability of spawning stone overlay gems
    maxCount?: number; // Maximum number of stone overlay gems
    specificPositions?: { row: number, col: number }[]; // Specific positions for stone overlays
}

export interface LevelConfig {
    id: number;
    name: string;
    board: {
        boardWidth: number;
        boardHeight: number;
        boardShape: 'rectangle' | 'square' | 'hexagon' | 'triangle';
        allowedGemTypes?: string[];
        obstacles?: ObstaclePosition[];
        stoneOverlays?: StoneOverlayConfig;
    };
    objectives: LevelObjective[];
    moveLimit: number;
    starThresholds: {
        one: number;
        two: number;
        three: number;
    };
}

export interface LevelState {
    currentLevelId: number;
    movesRemaining: number;
    isCompleted: boolean;
    isFailed: boolean;
}

export class LevelManager {
    private scene: Phaser.Scene;
    private currentLevel: LevelConfig | null = null;
    private state: LevelState;
    private levels: LevelConfig[] = [];


    // Temporary hard-coded levels for Phase 1
    private readonly TEMP_LEVELS: LevelConfig[] = [
        {
            id: 1,
            name: "Getting Started",
            board: { boardWidth: 3, boardHeight: 4, boardShape: 'rectangle' },
            objectives: [
                { type: 'collect', gemType: 'red', count: 8 },
                { type: 'collect', gemType: 'blue', count: 6 }
            ],
            moveLimit: 15,
            starThresholds: { one: 1000, two: 2500, three: 4500 }
        },
        {
            id: 2,
            name: "Color Collector",
            board: {
                boardWidth: 4,
                boardHeight: 4,
                boardShape: 'square',
                stoneOverlays: {
                    enabled: true,
                    chance: 0.3,
                    maxCount: 3
                }
            },
            objectives: [
                { type: 'collect', gemType: 'green', count: 12 },
                { type: 'collect', gemType: 'yellow', count: 10 },
                { type: 'collect', gemType: 'purple', count: 8 }
            ],
            moveLimit: 20,
            starThresholds: { one: 2000, two: 4500, three: 7500 }
        },
        {
            id: 3,
            name: "Rainbow Challenge",
            board: {
                boardWidth: 5,
                boardHeight: 5,
                boardShape: 'square',
                stoneOverlays: {
                    enabled: true,
                    specificPositions: [
                        { row: 1, col: 1 },
                        { row: 2, col: 2 },
                        { row: 3, col: 3 },
                        { row: 1, col: 3 },
                        { row: 3, col: 1 }
                    ]
                }
            },
            objectives: [
                { type: 'collect', gemType: 'red', count: 10 },
                { type: 'collect', gemType: 'blue', count: 10 },
                { type: 'collect', gemType: 'green', count: 10 },
                { type: 'collect', gemType: 'yellow', count: 10 }
            ],
            moveLimit: 25,
            starThresholds: { one: 3000, two: 6500, three: 11000 }
        }
    ];

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.state = {
            currentLevelId: 1,
            movesRemaining: 0,
            isCompleted: false,
            isFailed: false
        };


        this.loadLevelsFromCache();

        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('move-made', this.onMoveMade, this);
        this.scene.events.on('objective-completed', this.onObjectiveCompleted, this);
    }

    /**
     * Load và start level
     */
    public loadLevel(levelId: number): boolean {
        const source = this.levels && this.levels.length > 0 ? this.levels : this.TEMP_LEVELS;
        const levelConfig = source.find(level => level.id === levelId);

        if (!levelConfig) {
            console.error(`Level ${levelId} not found`);
            return false;
        }

        this.currentLevel = levelConfig;
        this.state = {
            currentLevelId: levelId,
            movesRemaining: levelConfig.moveLimit,
            isCompleted: false,
            isFailed: false
        };

        // Emit level started event
        this.scene.events.emit('level-started', {
            levelId,
            levelName: levelConfig.name,
            objectives: levelConfig.objectives,
            moveLimit: levelConfig.moveLimit,
            starThresholds: levelConfig.starThresholds,
            board: levelConfig.board
        });

        // Emit initial moves update
        this.scene.events.emit('moves-updated', this.state.movesRemaining);

        console.log(`Level ${levelId} loaded: ${levelConfig.name}`);
        return true;
    }

    /**
     * Consume một move
     */
    public consumeMove(): void {
        if (this.state.movesRemaining > 0) {
            this.state.movesRemaining--;

            // Emit moves updated
            this.scene.events.emit('moves-updated', this.state.movesRemaining);

            // Check for failure condition
            if (this.state.movesRemaining === 0 && !this.state.isCompleted) {
                this.onLevelFailed();
            }
        }
    }

    /**
     * Xử lý khi player makes a move
     */
    private onMoveMade(): void {
        this.consumeMove();
    }

    /**
     * Xử lý khi objectives completed
     */
    private onObjectiveCompleted(): void {
        if (!this.state.isCompleted && !this.state.isFailed) {
            this.state.isCompleted = true;
            this.onLevelCompleted();
        }
    }

    /**
     * Xử lý level completed
     */
    private onLevelCompleted(): void {
        console.log(`Level ${this.state.currentLevelId} completed!`);

        this.scene.events.emit('level-completed', {
            levelId: this.state.currentLevelId,
            movesUsed: this.getMoveLimit() - this.state.movesRemaining,
            movesRemaining: this.state.movesRemaining
        });
    }

    /**
     * Load next level
     */
    public loadNextLevel(): boolean {
        const nextLevelId = this.state.currentLevelId + 1;
        const source = this.levels && this.levels.length > 0 ? this.levels : this.TEMP_LEVELS;
        const nextLevel = source.find(level => level.id === nextLevelId);

        if (nextLevel) {
            return this.loadLevel(nextLevelId);
        } else {
            console.log('No more levels available');
            this.scene.events.emit('all-levels-completed');
            return false;
        }
    }

    /**
     * Restart current level
     */
    public restartLevel(): boolean {
        if (this.currentLevel) {
            // Reset game state
            this.scene.events.emit('game-reset-requested');
            return this.loadLevel(this.currentLevel.id);
        }
        return false;
    }

    /**
     * Xử lý level failed
     */
    private onLevelFailed(): void {
        if (!this.state.isCompleted) {
            this.state.isFailed = true;
            console.log(`Level ${this.state.currentLevelId} failed - out of moves`);

            this.scene.events.emit('level-failed', {
                levelId: this.state.currentLevelId,
                reason: 'out_of_moves'
            });
        }
    }

    /**
     * Reset current level
     */
    public resetLevel(): void {
        if (this.currentLevel) {
            this.loadLevel(this.currentLevel.id);
        }
    }

    // Getters
    public getCurrentLevel(): LevelConfig | null {
        return this.currentLevel;
    }

    public getObjectives(): LevelObjective[] {
        return this.currentLevel?.objectives || [];
    }

    public getMoveLimit(): number {
        return this.currentLevel?.moveLimit || 0;
    }

    public getMovesRemaining(): number {
        return this.state.movesRemaining;
    }

    public getStarThresholds(): { one: number; two: number; three: number } | null {
        return this.currentLevel?.starThresholds || null;
    }

    public getLevelState(): LevelState {
        return { ...this.state };
    }

    public isLevelCompleted(): boolean {
        return this.state.isCompleted;
    }

    public isLevelFailed(): boolean {
        return this.state.isFailed;
    }


	    /**
	     * Load levels from Phaser cache (populated by Preloader)
	     */
	    private loadLevelsFromCache(): void {
	        try {
	            const json: any = (this.scene as any).cache?.json?.get('levels');
	            const fromCache: LevelConfig[] | undefined = json?.levels;
	            if (Array.isArray(fromCache) && fromCache.length > 0) {
	                this.levels = fromCache as LevelConfig[];
	                console.log(`[LevelManager] Loaded ${this.levels.length} levels from JSON`);
	            } else {
	                this.levels = this.TEMP_LEVELS;
	                console.warn('[LevelManager] levels.json missing or invalid, using TEMP_LEVELS fallback');
	            }
	        } catch (e) {
	            console.warn('[LevelManager] Error loading levels from cache, using TEMP_LEVELS fallback', e);
	            this.levels = this.TEMP_LEVELS;
	        }
	    }

	    /**
	     * Helper getters
	     */
	    public getLevels(): LevelConfig[] { return this.levels; }
	    public getLevelById(id: number): LevelConfig | undefined { return this.levels.find(l => l.id === id); }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('move-made', this.onMoveMade, this);
        this.scene.events.off('objective-completed', this.onObjectiveCompleted, this);
    }
}
