/**
 * AudioManager - <PERSON><PERSON><PERSON><PERSON> lý tất cả audio trong game
 * Singleton pattern để access từ mọi nơi trong game
 */
export class AudioManager {
    private static instance: AudioManager | null = null;
    private scene: Phaser.Scene;
    private backgroundMusic: Phaser.Sound.BaseSound | null = null;
    private currentLevelMusic: Phaser.Sound.BaseSound | null = null;
    private activeSounds: Phaser.Sound.BaseSound[] = [];
    private soundEnabled: boolean = true;
    private musicEnabled: boolean = true;
    private masterVolume: number = 1.0;
    private musicVolume: number = 0.3;
    private sfxVolume: number = 0.7;

    // Audio keys mapping
    private readonly AUDIO_KEYS = {
        // Background music
        BACKGROUND_MUSIC: 'background_music',
        
        // UI sounds
        BUTTON_CLICK: 'button_click',
        
        // Gameplay sounds
        GEM_SWAP: 'gem_swap',
        MATCH_SMALL: 'match_small',
        MATCH_MEDIUM: 'match_medium', 
        MATCH_LARGE: 'match_large',
        
        // Special effects
        SPECIAL_GEM_ACTIVATE: 'special_gem_activate',
        POWER_UP_COLLECT: 'power_up_collect',
        POWER_UP_USE: 'power_up_use',
        
        // Combo sounds
        COMBO_LOW: 'combo_low',
        COMBO_HIGH: 'combo_high',
        
        // Level sounds
        LEVEL_START: 'level_start',
        LEVEL_COMPLETE: 'level_complete',
        LEVEL_FAIL: 'level_fail',
        
        // Star system
        STAR_EARNED: 'star_earned'
    };

    private constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.loadSettings();
        this.setupEventListeners();
    }

    /**
     * Get singleton instance
     */
    public static getInstance(scene?: Phaser.Scene): AudioManager {
        if (!AudioManager.instance && scene) {
            AudioManager.instance = new AudioManager(scene);
        }
        return AudioManager.instance!;
    }

    /**
     * Initialize AudioManager với scene mới
     */
    public static initialize(scene: Phaser.Scene): AudioManager {
        AudioManager.instance = new AudioManager(scene);
        return AudioManager.instance;
    }

    /**
     * Load audio settings từ localStorage
     */
    private loadSettings(): void {
        const soundEnabled = localStorage.getItem('match3_sound_enabled');
        const musicEnabled = localStorage.getItem('match3_music_enabled');
        const masterVolume = localStorage.getItem('match3_master_volume');
        const musicVolume = localStorage.getItem('match3_music_volume');
        const sfxVolume = localStorage.getItem('match3_sfx_volume');

        if (soundEnabled !== null) this.soundEnabled = soundEnabled === 'true';
        if (musicEnabled !== null) this.musicEnabled = musicEnabled === 'true';
        if (masterVolume !== null) this.masterVolume = parseFloat(masterVolume);
        if (musicVolume !== null) this.musicVolume = parseFloat(musicVolume);
        if (sfxVolume !== null) this.sfxVolume = parseFloat(sfxVolume);
    }

    /**
     * Save audio settings vào localStorage
     */
    private saveSettings(): void {
        localStorage.setItem('match3_sound_enabled', this.soundEnabled.toString());
        localStorage.setItem('match3_music_enabled', this.musicEnabled.toString());
        localStorage.setItem('match3_master_volume', this.masterVolume.toString());
        localStorage.setItem('match3_music_volume', this.musicVolume.toString());
        localStorage.setItem('match3_sfx_volume', this.sfxVolume.toString());
    }

    /**
     * Setup event listeners cho audio cues
     */
    private setupEventListeners(): void {
        // Gameplay events
        this.scene.events.on('move-made', () => this.playSound(this.AUDIO_KEYS.GEM_SWAP));
        this.scene.events.on('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.on('combo-achieved', this.onComboAchieved, this);
        this.scene.events.on('special-gem-activated', () => this.playSound(this.AUDIO_KEYS.SPECIAL_GEM_ACTIVATE));
        
        // Power-up events
        this.scene.events.on('power-up-awarded', () => this.playSound(this.AUDIO_KEYS.POWER_UP_COLLECT));
        this.scene.events.on('power-up-activated', () => this.playSound(this.AUDIO_KEYS.POWER_UP_USE));
        
        // Level events
        this.scene.events.on('level-started', this.onLevelStarted, this);
        this.scene.events.on('level-completed', this.onLevelCompleted, this);
        this.scene.events.on('level-failed', this.onLevelFailed, this);
        
        // Star events
        this.scene.events.on('stars-updated', this.onStarsUpdated, this);
    }

    /**
     * Handle matches processed event
     */
    private onMatchesProcessed(data: { matches: any[], isCombo: boolean }): void {
        const matchCount = data.matches.length;
        
        if (matchCount >= 5) {
            this.playSound(this.AUDIO_KEYS.MATCH_LARGE);
        } else if (matchCount >= 4) {
            this.playSound(this.AUDIO_KEYS.MATCH_MEDIUM);
        } else {
            this.playSound(this.AUDIO_KEYS.MATCH_SMALL);
        }
    }

    /**
     * Handle combo achieved event
     */
    private onComboAchieved(data: { comboCount: number }): void {
        if (data.comboCount >= 5) {
            this.playSound(this.AUDIO_KEYS.COMBO_HIGH);
        } else {
            this.playSound(this.AUDIO_KEYS.COMBO_LOW);
        }
    }

    /**
     * Handle level started event
     */
    private onLevelStarted(): void {
        // Stop all sounds first (including any previous level music)
        this.stopAllSounds();

        // Play level start music
        this.playLevelMusic(this.AUDIO_KEYS.LEVEL_START);

        // Start background music after level start music finishes (approximately 2 seconds)
        this.scene.time.delayedCall(2000, () => {
            // Only start background music if no level music is currently playing
            if (!this.currentLevelMusic || !this.currentLevelMusic.isPlaying) {
                this.playBackgroundMusic();
            }
        });
    }

    /**
     * Handle level completed event
     */
    private onLevelCompleted(): void {
        // Stop ALL sounds immediately - no other audio should play during completion
        this.stopAllSounds();

        // Play ONLY level complete music
        this.playLevelMusic(this.AUDIO_KEYS.LEVEL_COMPLETE);
    }

    /**
     * Handle level failed event
     */
    private onLevelFailed(): void {
        // Stop ALL sounds immediately
        this.stopAllSounds();

        // Play ONLY level fail music
        this.playLevelMusic(this.AUDIO_KEYS.LEVEL_FAIL);
    }

    /**
     * Handle stars updated event
     */
    private onStarsUpdated(data: { stars: number, starChanged: boolean }): void {
        if (data.starChanged) {
            this.playSound(this.AUDIO_KEYS.STAR_EARNED);
        }
    }

    /**
     * Stop all currently playing sounds
     */
    public stopAllSounds(): void {
        // Stop all active sound effects
        this.activeSounds.forEach(sound => {
            if (sound && sound.isPlaying) {
                sound.stop();
            }
        });
        this.activeSounds = [];

        // Stop background music
        this.stopBackgroundMusic();

        // Stop current level music
        this.stopLevelMusic();
    }

    /**
     * Stop level-specific music (level start, complete, fail)
     */
    public stopLevelMusic(): void {
        if (this.currentLevelMusic) {
            this.currentLevelMusic.stop();
            this.currentLevelMusic = null;
        }
    }

    /**
     * Play sound effect
     */
    public playSound(key: string, volume?: number, rate?: number): void {
        if (!this.soundEnabled || !this.scene.cache.audio.exists(key)) {
            return;
        }

        try {
            const finalVolume = (volume || 1.0) * this.sfxVolume * this.masterVolume;
            const sound = this.scene.sound.add(key, {
                volume: finalVolume,
                rate: rate || 1.0
            });

            // Track active sounds for cleanup
            this.activeSounds.push(sound);

            // Remove from tracking when sound completes
            sound.once('complete', () => {
                const index = this.activeSounds.indexOf(sound);
                if (index > -1) {
                    this.activeSounds.splice(index, 1);
                }
            });

            sound.play();
        } catch (error) {
            console.warn(`Failed to play sound ${key}:`, error);
        }
    }

    /**
     * Play level-specific music (level start, complete, fail)
     */
    private playLevelMusic(key: string, volume?: number): void {
        if (!this.soundEnabled || !this.scene.cache.audio.exists(key)) {
            return;
        }

        try {
            // Stop any existing level music first
            this.stopLevelMusic();

            const finalVolume = (volume || 1.0) * this.musicVolume * this.masterVolume;
            this.currentLevelMusic = this.scene.sound.add(key, {
                volume: finalVolume,
                loop: false
            });

            // Auto-cleanup when music completes
            this.currentLevelMusic.once('complete', () => {
                this.currentLevelMusic = null;
            });

            this.currentLevelMusic.play();
        } catch (error) {
            console.warn(`Failed to play level music ${key}:`, error);
        }
    }

    /**
     * Play background music
     */
    public playBackgroundMusic(): void {
        if (!this.musicEnabled || !this.scene.cache.audio.exists(this.AUDIO_KEYS.BACKGROUND_MUSIC)) {
            return;
        }

        try {
            if (this.backgroundMusic) {
                this.backgroundMusic.stop();
            }

            this.backgroundMusic = this.scene.sound.add(this.AUDIO_KEYS.BACKGROUND_MUSIC, {
                volume: this.musicVolume * this.masterVolume,
                loop: true
            });
            this.backgroundMusic.play();
        } catch (error) {
            console.warn('Failed to play background music:', error);
        }
    }

    /**
     * Stop background music
     */
    public stopBackgroundMusic(): void {
        if (this.backgroundMusic) {
            this.backgroundMusic.stop();
            this.backgroundMusic = null;
        }
    }

    /**
     * Prepare for scene transition - stop all audio cleanly
     */
    public prepareForSceneTransition(): void {
        console.log('AudioManager: Preparing for scene transition...');
        this.stopAllSounds();
    }

    /**
     * Toggle sound on/off
     */
    public toggleSound(): boolean {
        this.soundEnabled = !this.soundEnabled;
        this.saveSettings();
        return this.soundEnabled;
    }

    /**
     * Toggle music on/off
     */
    public toggleMusic(): boolean {
        this.musicEnabled = !this.musicEnabled;
        
        if (this.musicEnabled) {
            this.playBackgroundMusic();
        } else {
            this.stopBackgroundMusic();
        }
        
        this.saveSettings();
        return this.musicEnabled;
    }

    /**
     * Set master volume (0.0 - 1.0)
     */
    public setMasterVolume(volume: number): void {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        
        // Update background music volume
        if (this.backgroundMusic) {
            (this.backgroundMusic as any).setVolume(this.musicVolume * this.masterVolume);
        }
        
        this.saveSettings();
    }

    /**
     * Set music volume (0.0 - 1.0)
     */
    public setMusicVolume(volume: number): void {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        // Update background music volume
        if (this.backgroundMusic) {
            (this.backgroundMusic as any).setVolume(this.musicVolume * this.masterVolume);
        }
        
        this.saveSettings();
    }

    /**
     * Set SFX volume (0.0 - 1.0)
     */
    public setSfxVolume(volume: number): void {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
        this.saveSettings();
    }

    // Getters
    public isSoundEnabled(): boolean { return this.soundEnabled; }
    public isMusicEnabled(): boolean { return this.musicEnabled; }
    public getMasterVolume(): number { return this.masterVolume; }
    public getMusicVolume(): number { return this.musicVolume; }
    public getSfxVolume(): number { return this.sfxVolume; }

    /**
     * Cleanup
     */
    public destroy(): void {
        // Stop all audio
        this.stopAllSounds();

        // Remove event listeners
        this.scene.events.off('move-made');
        this.scene.events.off('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.off('combo-achieved', this.onComboAchieved, this);
        this.scene.events.off('special-gem-activated');
        this.scene.events.off('power-up-awarded');
        this.scene.events.off('power-up-activated');
        this.scene.events.off('level-started', this.onLevelStarted, this);
        this.scene.events.off('level-completed', this.onLevelCompleted, this);
        this.scene.events.off('level-failed', this.onLevelFailed, this);
        this.scene.events.off('stars-updated', this.onStarsUpdated, this);

        AudioManager.instance = null;
    }
}
