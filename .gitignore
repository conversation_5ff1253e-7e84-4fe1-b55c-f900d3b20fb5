# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Bun specific
.bun/
bun.lockb

# Build outputs
dist/
dist-ssr/
build/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Logs
logs/
*.log

# IDE and Editor files
.vscode/*
!.vscode/extensions.json
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript cache
*.tsbuildinfo

# Coverage and testing
coverage/
*.lcov
.nyc_output/
test-results/
playwright-report/
playwright/.cache/

# Cache directories
.cache/
.parcel-cache/
.vite/
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Temporary folders
tmp/
temp/

# Backup files
*.bak
*.backup
*.old

# Game specific - keep assets but ignore generated ones
!public/assets/**
!src/assets/**
public/assets/generated/
src/assets/temp/
