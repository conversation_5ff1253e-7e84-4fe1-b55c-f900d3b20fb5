/**
 * Enum cho các loại power-up
 */
export enum PowerUpType {
    BOMB = 'bomb',
    LIGHTNING = 'lightning',
    HAMMER = 'hammer',
    SHUFFLE = 'shuffle',
    COLOR_BLAST = 'color_blast',
    HINT = 'hint'
}

/**
 * Interface cho power-up properties
 */
export interface PowerUpProperties {
    type: PowerUpType;
    name: string;
    description: string;
    icon: string;
    cooldown: number; // Time-based cooldown (legacy)
    moveCooldown: number; // New: move-based cooldown
    maxUses?: number;
    cost?: number;
}

/**
 * Interface cho power-up activation data
 */
export interface PowerUpActivationData {
    type: PowerUpType;
    position?: { x: number; y: number; gridX?: number; gridY?: number };
    targetColor?: number;
    intensity?: number;
}

/**
 * Interface cho power-up inventory item
 */
export interface PowerUpInventoryItem {
    type: PowerUpType;
    quantity: number;
    lastUsed?: number;
    isOnCooldown: boolean;
    cooldownMovesRemaining?: number; // New: move-based cooldown
}

/**
 * Utility class cho power-up operations
 */
export class PowerUpUtils {
    /**
     * Get power-up properties
     */
    static getPowerUpProperties(type: PowerUpType): PowerUpProperties {
        const properties: Record<PowerUpType, PowerUpProperties> = {
            [PowerUpType.BOMB]: {
                type: PowerUpType.BOMB,
                name: 'Bomb',
                description: 'Destroys 5x5 area around target',
                icon: 'bomb_icon',
                cooldown: 3000, // 3 seconds (legacy)
                moveCooldown: 3, // 3 moves cooldown
                maxUses: 3,
                cost: 100
            },
            [PowerUpType.LIGHTNING]: {
                type: PowerUpType.LIGHTNING,
                name: 'Lightning',
                description: 'Destroys entire row and column',
                icon: 'lightning_icon',
                cooldown: 5000, // 5 seconds (legacy)
                moveCooldown: 2, // 2 moves cooldown
                maxUses: 2,
                cost: 150
            },
            [PowerUpType.HAMMER]: {
                type: PowerUpType.HAMMER,
                name: 'Hammer',
                description: 'Destroys any single gem',
                icon: 'hammer_icon',
                cooldown: 1000, // 1 second (legacy)
                moveCooldown: 1, // 1 move cooldown
                maxUses: 5,
                cost: 50
            },
            [PowerUpType.SHUFFLE]: {
                type: PowerUpType.SHUFFLE,
                name: 'Shuffle',
                description: 'Shuffles entire board',
                icon: 'shuffle_icon',
                cooldown: 10000, // 10 seconds (legacy)
                moveCooldown: 4, // 4 moves cooldown (most powerful)
                maxUses: 1,
                cost: 200
            },
            [PowerUpType.COLOR_BLAST]: {
                type: PowerUpType.COLOR_BLAST,
                name: 'Color Blast',
                description: 'Destroys all gems of selected color',
                icon: 'color_blast_icon',
                cooldown: 8000, // 8 seconds (legacy)
                moveCooldown: 4, // 4 moves cooldown (powerful)
                maxUses: 2,
                cost: 180
            },
            [PowerUpType.HINT]: {
                type: PowerUpType.HINT,
                name: 'Hint',
                description: 'Shows possible moves on the board',
                icon: 'hint_icon',
                cooldown: 2000, // 2 seconds (legacy)
                moveCooldown: 0, // No cooldown for hints
                maxUses: 10,
                cost: 25
            }
        };

        return properties[type];
    }

    /**
     * Check if power-up is available (not on cooldown)
     */
    static isAvailable(item: PowerUpInventoryItem): boolean {
        if (item.quantity <= 0) return false;
        if (!item.isOnCooldown) return true;

        // Check move-based cooldown first (new system)
        if (item.cooldownMovesRemaining !== undefined) {
            return item.cooldownMovesRemaining <= 0;
        }

        // Fallback to time-based cooldown (legacy)
        const now = Date.now();
        const properties = this.getPowerUpProperties(item.type);
        const timeSinceLastUse = now - (item.lastUsed || 0);

        return timeSinceLastUse >= properties.cooldown;
    }

    /**
     * Get remaining cooldown time (legacy time-based)
     */
    static getRemainingCooldown(item: PowerUpInventoryItem): number {
        if (!item.isOnCooldown || !item.lastUsed) return 0;

        const now = Date.now();
        const properties = this.getPowerUpProperties(item.type);
        const elapsed = now - item.lastUsed;

        return Math.max(0, properties.cooldown - elapsed);
    }

    /**
     * Get move-based cooldown duration for power-up type
     */
    static getMoveCooldown(type: PowerUpType): number {
        const properties = this.getPowerUpProperties(type);
        return properties.moveCooldown;
    }

    /**
     * Get remaining move-based cooldown
     */
    static getRemainingMoveCooldown(item: PowerUpInventoryItem): number {
        if (!item.isOnCooldown) return 0;
        return item.cooldownMovesRemaining || 0;
    }

    /**
     * Get power-up texture/icon name (legacy method)
     */
    static getPowerUpTexture(type: PowerUpType): string {
        const textures: Record<PowerUpType, string> = {
            [PowerUpType.HINT]: 'power_up_1',        // power_up_1.png
            [PowerUpType.HAMMER]: 'power_up_2',      // power_up_2.png
            [PowerUpType.BOMB]: 'power_up_3',        // power_up_3.png
            [PowerUpType.LIGHTNING]: 'power_up_4',   // power_up_4.png
            [PowerUpType.SHUFFLE]: 'power_up_5',     // power_up_5.png
            [PowerUpType.COLOR_BLAST]: 'power_up_6'  // power_up_6.png
        };

        return textures[type];
    }

    /**
     * Get power-up texture with atlas support
     */
    static getPowerUpTextureWithAtlas(scene: Phaser.Scene, type: PowerUpType): { texture: string, frame?: string } {
        // Check if atlas is available
        const atlasAvailable = scene.registry.get('atlasAvailable') && scene.textures.exists('game_atlas');

        const textures: Record<PowerUpType, string> = {
            [PowerUpType.HINT]: 'power_up_1',
            [PowerUpType.HAMMER]: 'power_up_2',
            [PowerUpType.BOMB]: 'power_up_3',
            [PowerUpType.LIGHTNING]: 'power_up_4',
            [PowerUpType.SHUFFLE]: 'power_up_5',
            [PowerUpType.COLOR_BLAST]: 'power_up_6'
        };

        const frameName = textures[type];

        if (atlasAvailable) {
            return { texture: 'game_atlas', frame: frameName };
        } else {
            return { texture: frameName };
        }
    }

    /**
     * Get power-up effect color
     */
    static getPowerUpColor(type: PowerUpType): number {
        const colors: Record<PowerUpType, number> = {
            [PowerUpType.BOMB]: 0xff4444,      // Red
            [PowerUpType.LIGHTNING]: 0xffff44, // Yellow
            [PowerUpType.HAMMER]: 0x888888,    // Gray
            [PowerUpType.SHUFFLE]: 0x44ff44,   // Green
            [PowerUpType.COLOR_BLAST]: 0xff44ff, // Magenta
            [PowerUpType.HINT]: 0x44ffff       // Cyan
        };

        return colors[type];
    }
}
