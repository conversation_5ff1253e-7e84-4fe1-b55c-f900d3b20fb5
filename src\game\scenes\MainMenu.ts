import { Scene, GameObjects } from 'phaser';
import { AudioManager } from '../../managers/AudioManager';

export class MainMenu extends Scene
{
    background: GameObjects.Image;
    logo: GameObjects.Image;
    playButton: GameObjects.Container;

    constructor ()
    {
        super('MainMenu');
    }

    create ()
    {
        this.background = this.add.image(512, 384, 'background');

        this.logo = this.add.image(512, 300, 'logo');

        // Create Play button
        this.createPlayButton();
    }

    private createPlayButton(): void {
        // Create button container
        this.playButton = this.add.container(512, 460);

        // Button background
        const buttonBg = this.add.rectangle(0, 0, 200, 60, 0x4CAF50);
        buttonBg.setStrokeStyle(3, 0x2E7D32);
        buttonBg.setInteractive();

        // Button text
        const buttonText = this.add.text(0, 0, 'PLAY', {
            fontFamily: 'Arial Black',
            fontSize: '32px',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3,
            align: 'center'
        }).setOrigin(0.5);

        // Add elements to container
        this.playButton.add([buttonBg, buttonText]);

        // Hover effects
        buttonBg.on('pointerover', () => {
            buttonBg.setFillStyle(0x66BB6A);
            this.tweens.add({
                targets: this.playButton,
                scaleX: 1.1,
                scaleY: 1.1,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        buttonBg.on('pointerout', () => {
            buttonBg.setFillStyle(0x4CAF50);
            this.tweens.add({
                targets: this.playButton,
                scaleX: 1,
                scaleY: 1,
                duration: 150,
                ease: 'Back.easeOut'
            });
        });

        // Click handler
        buttonBg.on('pointerdown', () => {
            // Play button click sound
            const audioManager = AudioManager.getInstance(this);
            if (audioManager) {
                audioManager.playSound('button_click');
            }

            // Button press animation
            this.tweens.add({
                targets: this.playButton,
                scaleX: 0.95,
                scaleY: 0.95,
                duration: 100,
                yoyo: true,
                onComplete: () => {
                    // Start Game scene with an explicit levelId (Phase 3)
                    this.scene.start('Game', { levelId: 1 });
                }
            });
        });
    }
}
