# 🎮 Match 3 Game - Level Creation Guide

## 📋 Tổng Quan

Hướng dẫn chi tiết về cách tạo và quản lý level trong Match 3 game, bao gồm 5 yếu tố chính:
1. **<PERSON><PERSON>nh dáng Board Game** (Board Shape)
2. **<PERSON><PERSON>ch thước Board Game** (Board Dimensions)
3. **T<PERSON>h năng Unlock** (Gem Unlock System)
4. **<PERSON>ệ thống Chướng ngại vật** (Obstacles System)
5. **Stone Overlay System**

---

## 🏗️ Cấu Trúc Level Configuration

### Level Schema
```json
{
  "id": number,
  "name": string,
  "board": {
    "boardWidth": number,
    "boardHeight": number,
    "boardShape": "rectangle" | "square" | "hexagon" | "triangle",
    "obstacles": ObstaclePosition[],
    "stoneOverlays": StoneOverlayConfig
  },
  "objectives": LevelObjective[],
  "moveLimit": number,
  "starThresholds": {
    "one": number,
    "two": number,
    "three": number
  }
}
```

### V<PERSON> dụ Level hoàn chỉnh
```json
{
  "id": 4,
  "name": "Gem Master",
  "board": {
    "boardWidth": 6,
    "boardHeight": 5,
    "boardShape": "rectangle",
    "obstacles": [
      { "row": 2, "col": 2, "type": "ice" },
      { "row": 2, "col": 3, "type": "metal" },
      { "row": 1, "col": 4, "type": "stone" },
      { "row": 3, "col": 1, "type": "wood" }
    ],
    "stoneOverlays": {
      "enabled": true,
      "specificPositions": [
        { "row": 0, "col": 1 },
        { "row": 0, "col": 4 },
        { "row": 4, "col": 0 },
        { "row": 4, "col": 5 }
      ]
    }
  },
  "objectives": [
    { "type": "collect", "gemType": "purple", "count": 18 },
    { "type": "collect", "gemType": "orange", "count": 15 },
    { "type": "destroy-stone-overlays", "count": 6 }
  ],
  "moveLimit": 30,
  "starThresholds": { "one": 4000, "two": 8500, "three": 14000 }
}
```

---

## 🔷 1. Hình Dáng Board Game (Board Shape)

### Các hình dáng hỗ trợ

#### Rectangle & Square
```typescript
case 'rectangle':
case 'square':
    // Render tất cả cells trong grid
    return true;
```
- **Rectangle**: Hình chữ nhật với width ≠ height
- **Square**: Hình vuông với width = height
- **Use case**: Level cơ bản, dễ hiểu cho người chơi mới

#### Hexagon
```typescript
case 'hexagon':
    const centerRow = Math.floor(this.boardHeight / 2);
    const centerCol = Math.floor(this.boardWidth / 2);
    const maxRadius = Math.min(centerRow, centerCol);
    const distanceFromCenter = Math.abs(row - centerRow) + Math.abs(col - centerCol);
    return distanceFromCenter <= maxRadius;
```
- **Logic**: Sử dụng Manhattan distance từ center
- **Visual**: Tạo hình lục giác bằng cách bỏ các góc
- **Use case**: Level trung bình, tăng độ khó

#### Triangle
```typescript
case 'triangle':
    const maxColsInRow = Math.min(row + 1, this.boardWidth);
    const startCol = Math.floor((this.boardWidth - maxColsInRow) / 2);
    const endCol = startCol + maxColsInRow - 1;
    return col >= startCol && col <= endCol;
```
- **Logic**: Pyramid shape từ trên xuống dưới
- **Visual**: Mỗi row có ít cells hơn row trước
- **Use case**: Level khó, thử thách strategy

### Implementation
```typescript
// File: src/managers/GameBoard.ts
private shouldRenderCell(row: number, col: number): boolean {
    switch (this.boardShape) {
        case 'rectangle':
        case 'square':
            return true;
        case 'hexagon':
            // Hexagon logic
        case 'triangle':
            // Triangle logic
        default:
            return true;
    }
}
```

---

## 📏 2. Kích Thước Board Game

### Thông số kỹ thuật
```typescript
interface BoardDimensions {
    boardWidth: number;   // 3-12 cells (recommended: 4-8)
    boardHeight: number;  // 3-12 cells (recommended: 4-8)
}
```

### Kích thước phổ biến
| Level Type | Width x Height | Difficulty | Use Case |
|------------|----------------|------------|----------|
| Beginner | 4x4, 4x5 | Easy | Tutorial, early levels |
| Intermediate | 5x5, 6x5 | Medium | Main progression |
| Advanced | 6x6, 7x6, 8x6 | Hard | Challenge levels |
| Expert | 8x8+ | Very Hard | End-game content |

### Responsive UI System
```typescript
// File: src/managers/GameBoard.ts
private calculateBoardPosition(): void {
    const totalBoardWidth = this.boardWidth * this.cellSize;
    const totalBoardHeight = this.boardHeight * this.cellSize;
    
    this.startX = (this.scene.scale.width - totalBoardWidth) / 2;
    this.startY = (this.scene.scale.height - totalBoardHeight) / 2;
}
```

---

## 🔓 3. Tính Năng Unlock (Gem Unlock System)

### Gem Types & Unlock Schedule
```typescript
// Basic Gems (Always Available)
Level 1-5: RED, BLUE, GREEN, YELLOW, PURPLE, ORANGE, WHITE

// Advanced Gems (Progressive Unlock)
Level 6:  + DIAMOND
Level 8:  + RUBY  
Level 9:  + EMERALD
Level 11: + SAPPHIRE
Level 12: + CRYSTAL
Level 14: + MOONSTONE
Level 15: + OBSIDIAN
Level 17: + FIRE_OPAL
Level 18: + ICE_CRYSTAL
Level 20: + LIGHTNING
```

### Implementation
```typescript
// File: src/managers/GemUnlockManager.ts
public getAvailableGemsForLevel(currentLevel: number): GemType[] {
    const availableGems = [...GemTypeUtils.getBasicGemTypes()];
    
    this.gemUnlockConfigs.forEach(config => {
        if (currentLevel >= config.unlockLevel && this.isGemUnlocked(config.gemType)) {
            availableGems.push(config.gemType);
        }
    });
    
    return availableGems;
}
```

### Unlock Configuration
```typescript
interface GemUnlockConfig {
    gemType: GemType;
    unlockLevel: number;
    name: string;
    description: string;
}
```

### Persistence
- **Storage**: localStorage với key `match3_unlocked_gems`
- **Format**: JSON array của unlocked gem types
- **Auto-save**: Sau mỗi level completion
- **Auto-load**: Khi khởi động game

---

## 🚧 4. Hệ Thống Chướng Ngại Vật (Obstacles)

### Obstacle Types
```typescript
enum ObstacleType {
    STONE = 'stone',    // Không thể phá hủy
    WOOD = 'wood',      // Có thể phá hủy bằng special gems
    ICE = 'ice',        // Có thể phá hủy, block gravity
    METAL = 'metal'     // Rất khó phá hủy, high durability
}
```

### Obstacle Properties
```typescript
interface ObstacleProperties {
    isDestructible: boolean;        // Có thể phá hủy không
    durability?: number;            // Độ bền (hits to destroy)
    blockMovement: boolean;         // Block gem swapping
    blockGravity: boolean;          // Block gems falling through
    canBeAffectedBySpecial: boolean; // Bị ảnh hưởng bởi special gems
}
```

### Configuration trong Level
```json
"obstacles": [
    { "row": 1, "col": 1, "type": "stone" },
    { "row": 2, "col": 2, "type": "wood" },
    { "row": 2, "col": 3, "type": "ice" },
    { "row": 1, "col": 4, "type": "metal" }
]
```

### Obstacle Behavior
| Type | Destructible | Durability | Block Movement | Block Gravity | Special Effect |
|------|-------------|------------|----------------|---------------|----------------|
| Stone | ❌ | ∞ | ✅ | ✅ | Permanent barrier |
| Wood | ✅ | 1 | ✅ | ❌ | Easy to destroy |
| Ice | ✅ | 1 | ❌ | ✅ | Blocks falling gems |
| Metal | ✅ | 3 | ✅ | ✅ | Very durable |

---

## 🗿 5. Stone Overlay System

### Configuration Options

#### Random Placement
```json
"stoneOverlays": {
    "enabled": true,
    "chance": 0.25,      // 25% probability per cell
    "maxCount": 3        // Maximum 3 stone overlays
}
```

#### Fixed Positions
```json
"stoneOverlays": {
    "enabled": true,
    "specificPositions": [
        { "row": 0, "col": 1 },
        { "row": 0, "col": 4 },
        { "row": 4, "col": 0 },
        { "row": 4, "col": 5 }
    ]
}
```

### Stone Overlay Mechanics
- **Visual**: Gem với stone texture overlay
- **Behavior**: Cần match để remove stone layer
- **Objective**: Có thể là objective type `destroy-stone-overlays`
- **Strategy**: Tạo thêm layer complexity cho level

### Implementation
```typescript
// File: src/managers/StoneOverlayManager.ts
public addRandomStoneOverlays(gems: (Gem | null)[][], count: number): number {
    const availableGems = this.getAvailableGems(gems);
    const actualCount = Math.min(count, availableGems.length);
    
    for (let i = 0; i < actualCount; i++) {
        this.addStoneOverlayToGem(availableGems[i]);
    }
    
    return actualCount;
}
```

---

## 🎯 Level Design Best Practices

### 1. Progressive Difficulty
```
Level 1-5:   Tutorial (4x4, basic gems, simple objectives)
Level 6-10:  Easy (4x5, +obstacles, stone overlays)
Level 11-15: Medium (5x5, +shapes, multiple objectives)
Level 16-20: Hard (6x6, +advanced gems, complex layouts)
Level 21+:   Expert (7x7+, all features, high targets)
```

### 2. Objective Balance
```json
// Good balance example
"objectives": [
    { "type": "collect", "gemType": "blue", "count": 15 },      // Primary
    { "type": "collect", "gemType": "red", "count": 10 },       // Secondary  
    { "type": "destroy-stone-overlays", "count": 4 }            // Special
]
```

### 3. Move Limit Guidelines
| Board Size | Easy | Medium | Hard |
|------------|------|--------|------|
| 4x4 | 15-20 | 12-15 | 8-12 |
| 5x5 | 20-25 | 15-20 | 12-15 |
| 6x6 | 25-30 | 20-25 | 15-20 |
| 7x7+ | 30-40 | 25-30 | 20-25 |

### 4. Star Threshold Formula
```typescript
// Recommended ratios
const baseScore = moveLimit * 100;
const starThresholds = {
    one: baseScore * 0.5,      // 50% of base
    two: baseScore * 1.2,      // 120% of base  
    three: baseScore * 2.0     // 200% of base
};
```

---

## 🔧 Quy Trình Tạo Level

### Bước 1: Planning
1. **Xác định difficulty tier** (Beginner/Intermediate/Advanced/Expert)
2. **Chọn board shape & size** phù hợp với tier
3. **Design layout** với obstacles và stone overlays
4. **Set objectives** cân bằng và thú vị
5. **Calculate move limit** và star thresholds

### Bước 2: Implementation
1. **Thêm level config** vào `public/assets/data/levels.json`
2. **Test level** trong game
3. **Adjust parameters** nếu cần
4. **Validate balance** với playtesting

### Bước 3: Integration
```typescript
// File: src/managers/LevelManager.ts
private loadLevelsFromCache(): void {
    const cachedLevels = this.scene.cache.json.get('levels');
    if (cachedLevels?.levels) {
        this.levels = cachedLevels.levels;
        console.log(`Loaded ${this.levels.length} levels from JSON`);
    } else {
        this.levels = this.TEMP_LEVELS;
        console.log('Using fallback levels');
    }
}
```

---

## 📊 Level Validation Checklist

### ✅ Required Checks
- [ ] **ID unique** và sequential
- [ ] **Board dimensions** trong range hợp lệ (3-12)
- [ ] **Obstacle positions** trong board bounds
- [ ] **Stone overlay positions** valid
- [ ] **Objectives** có ít nhất 1 item
- [ ] **Move limit** > 0
- [ ] **Star thresholds** progressive (one < two < three)

### ✅ Balance Checks  
- [ ] **Objectives achievable** trong move limit
- [ ] **Difficulty appropriate** cho level position
- [ ] **Stone overlay count** không quá nhiều (< 50% board)
- [ ] **Obstacle placement** tạo strategy, không block hoàn toàn

### ✅ Technical Checks
- [ ] **JSON syntax** valid
- [ ] **Gem types** tồn tại trong system
- [ ] **Obstacle types** supported
- [ ] **Board shape** implemented

---

## 🚀 Advanced Features

### Dynamic Level Generation (Future)
```typescript
interface LevelTemplate {
    difficulty: DifficultyLevel;
    pattern: 'collection' | 'destruction' | 'mixed';
    specialFeatures: string[];
}

class LevelGenerator {
    generateLevel(template: LevelTemplate, seed: number): LevelConfig {
        // Procedural level generation logic
    }
}
```

### Level Analytics
```typescript
interface LevelStats {
    completionRate: number;
    averageAttempts: number;
    averageScore: number;
    starDistribution: { one: number; two: number; three: number };
}
```

---

## 📁 File Structure

```
src/
├── managers/
│   ├── LevelManager.ts          # Level loading & state management
│   ├── GameBoard.ts             # Board creation & shape logic
│   ├── GemUnlockManager.ts      # Gem unlock system
│   └── StoneOverlayManager.ts   # Stone overlay management
├── objects/
│   └── Obstacle.ts              # Obstacle implementation
├── types/
│   └── ObstacleTypes.ts         # Obstacle type definitions
└── game/scenes/
    └── Game.ts                  # Level integration

public/assets/data/
└── levels.json                  # Level configurations

docs/
├── LEVEL_CREATION_GUIDE.md      # This file
└── GEM_UNLOCK_SYSTEM.md         # Gem unlock details
```

---

## 🎮 Kết Luận

Hệ thống tạo level trong Match 3 game được thiết kế với 5 yếu tố chính hoạt động seamlessly cùng nhau:

1. **Board Shapes** tạo visual variety và strategic depth
2. **Flexible Dimensions** cho progressive difficulty scaling  
3. **Gem Unlock System** tạo long-term progression motivation
4. **Obstacles** thêm puzzle complexity và strategic thinking
5. **Stone Overlays** tạo additional layer objectives

Tất cả được quản lý thông qua JSON configuration đơn giản nhưng mạnh mẽ, cho phép rapid level creation và easy balancing! 🚀
