import { Scene } from 'phaser';
import { GameBoard } from '../../managers/GameBoard';
import { ScoreManager } from '../../managers/ScoreManager';
import { UIManager } from '../../managers/UIManager';
import { ParticleManager } from '../../managers/ParticleManager';
import { PowerUpManager } from '../../managers/PowerUpManager';
import { PowerUpPanel } from '../../ui/PowerUpPanel';
import { PowerUpCollectionSystem } from '../../managers/PowerUpCollectionSystem';
import { LevelManager, StoneOverlayConfig } from '../../managers/LevelManager';
import { ObjectiveManager } from '../../managers/ObjectiveManager';
import { EndLevelModal } from '../../ui/EndLevelModal';
import { StarRatingService } from '../../services/StarRatingService';
import { ObstaclePosition } from '../../types/ObstacleTypes';
import { AudioManager } from '../../managers/AudioManager';
import { GemUnlockManager } from '../../managers/GemUnlockManager';
import { GemUnlockNotification } from '../../ui/GemUnlockNotification';

export class Game extends Scene
{
    private gameBoard: GameBoard | null;
    private scoreManager: ScoreManager;
    private uiManager: UIManager;
    private particleManager: ParticleManager;
    private powerUpManager: PowerUpManager;
    private powerUpPanel: PowerUpPanel;
    private powerUpCollectionSystem: PowerUpCollectionSystem;
    private levelManager: LevelManager;
    private objectiveManager: ObjectiveManager;
    private endLevelModal: EndLevelModal;
    private starRatingService: StarRatingService;
    private audioManager: AudioManager;
    private gemUnlockManager: GemUnlockManager;
    private gemUnlockNotification: GemUnlockNotification;

    constructor ()
    {
        super('Game');
    }


    // Accept levelId from scene start
    create (data: { levelId?: number } = {})
    {

        // Tạo background
        this.add.image(512, 384, 'background');

        // Initialize managers in correct order
        this.audioManager = AudioManager.initialize(this);
        this.scoreManager = new ScoreManager(this);
        this.levelManager = new LevelManager(this);
        this.objectiveManager = new ObjectiveManager(this);
        this.uiManager = new UIManager(this);
        this.particleManager = new ParticleManager(this);
        this.powerUpManager = new PowerUpManager(this);
        this.powerUpPanel = new PowerUpPanel(this);
        this.powerUpCollectionSystem = new PowerUpCollectionSystem(this, this.powerUpManager);
        this.endLevelModal = new EndLevelModal(this);
        this.starRatingService = new StarRatingService(this);
        this.gemUnlockManager = GemUnlockManager.getInstance();
        this.gemUnlockNotification = new GemUnlockNotification(this);

        // Load gem unlock progress
        this.gemUnlockManager.loadUnlockProgress();

        // Setup level progression event handlers
        this.setupLevelProgressionHandlers();

        // Create game title and instructions
        this.createGameTitle();

        // GameBoard will be created when level starts
        this.gameBoard = null;

        // Setup input
        this.setupInput();

        // Initialize UI with current scores
        this.uiManager.updateHighScore(this.scoreManager.getHighScore());

        // Start level by id (from scene data or default 1)
        const startLevelId = data?.levelId ?? 1;
        this.levelManager.loadLevel(startLevelId);
    }

    private createGameTitle(): void {
        // Remove game title - objectives will be displayed here instead

        // Instructions
        const instructions = this.add.text(512, 700, 'Click gems to select, then click adjacent gem to swap', {
            fontFamily: 'Arial',
            fontSize: '20px',
            color: '#cccccc',
            align: 'center'
        });
        instructions.setOrigin(0.5);
    }

    private setupInput(): void {
        // ESC để về menu
        this.input.keyboard?.on('keydown-ESC', () => {
            this.scene.start('MainMenu');
        });

        // Handle board clicks for power-ups
        this.input.on('pointerdown', (pointer: Phaser.Input.Pointer) => {
            // Check if power-up is selected and gameBoard exists
            const selectedPowerUp = this.powerUpManager.getSelectedPowerUp();
            if (selectedPowerUp && this.gameBoard) {
                // Convert world coordinates to grid coordinates using board dimensions
                const startX = this.gameBoard.getStartX();
                const startY = this.gameBoard.getStartY();
                const cellSize = this.gameBoard.getCellSize();
                const boardWidth = this.gameBoard.getBoardWidth();
                const boardHeight = this.gameBoard.getBoardHeight();

                const gridX = Math.floor((pointer.worldX - startX) / cellSize);
                const gridY = Math.floor((pointer.worldY - startY) / cellSize);

                // Check if click is within board bounds
                if (gridX >= 0 && gridX < boardWidth && gridY >= 0 && gridY < boardHeight) {
                    this.events.emit('board-clicked', {
                        gridX,
                        gridY,
                        worldX: pointer.worldX,
                        worldY: pointer.worldY
                    });
                }
            }
        });
    }

    /**
     * Setup level progression event handlers
     */
    private setupLevelProgressionHandlers(): void {
        this.events.on('level-started', this.onLevelStarted, this);
        this.events.on('level-completed', this.onLevelCompleted, this);
        this.events.on('next-level-requested', this.onNextLevelRequested, this);
        this.events.on('retry-level-requested', this.onRetryLevelRequested, this);
        this.events.on('game-reset-requested', this.onGameResetRequested, this);
        this.events.on('all-levels-completed', this.onAllLevelsCompleted, this);
    }

    /**
     * Handle level started - create GameBoard with board config
     */
    private onLevelStarted(data: { levelId: number; board: { boardWidth: number; boardHeight: number; boardShape: 'rectangle' | 'square' | 'hexagon' | 'triangle'; obstacles?: ObstaclePosition[]; stoneOverlays?: StoneOverlayConfig } }): void {
        console.log('Level started with board config:', data.board);

        // Destroy existing GameBoard if it exists
        if (this.gameBoard) {
            this.gameBoard.destroy();
        }

        // Create new GameBoard with board config
        this.gameBoard = new GameBoard(this, this.scoreManager, data.board);

        // Set current level for gem unlock system
        this.gameBoard.setCurrentLevel(data.levelId);

        // Update UI positioning based on new board dimensions
        this.updateUIForBoard(data.board);
    }

    /**
     * Update UI positioning for new board dimensions
     */
    private updateUIForBoard(boardConfig: { boardWidth: number; boardHeight: number; boardShape: 'rectangle' | 'square' | 'hexagon' | 'triangle' }): void {
        // Calculate board area dimensions
        const cellSize = 60; // Same as GameBoard cellSize
        const framePadding = 12;
        const frameThickness = 8;

        const totalBoardWidth = boardConfig.boardWidth * cellSize;
        const totalBoardHeight = boardConfig.boardHeight * cellSize;
        const totalFrameWidth = totalBoardWidth + (framePadding * 2) + (frameThickness * 2);
        const totalFrameHeight = totalBoardHeight + (framePadding * 2) + (frameThickness * 2);

        // Calculate board center position
        const boardCenterX = 512; // Always center horizontally
        const boardCenterY = 384; // Keep UI elements at original position

        // Update UI manager with new board positioning
        this.uiManager.updateBoardPosition(boardCenterX, boardCenterY, totalFrameWidth, totalFrameHeight);
    }

    /**
     * Handle level completed - unlock new gems
     */
    private onLevelCompleted(data: { levelId: number; movesUsed: number; movesRemaining: number }): void {
        console.log(`Level ${data.levelId} completed!`);

        // Check for newly unlocked gems
        const newlyUnlocked = this.gemUnlockManager.unlockGemsForLevel(data.levelId);

        if (newlyUnlocked.length > 0) {
            console.log('New gems unlocked:', newlyUnlocked);

            // Emit gem unlock event for UI notification
            this.events.emit('gems-unlocked', {
                levelId: data.levelId,
                newGems: newlyUnlocked,
                configs: newlyUnlocked.map(gemType => this.gemUnlockManager.getUnlockConfig(gemType))
            });
        }
    }

    /**
     * Handle next level request
     */
    private onNextLevelRequested(): void {
        console.log('Next level requested');

        // Stop all audio before transitioning
        this.audioManager.prepareForSceneTransition();

        // Reset game state
        this.resetGameState();

        // Load next level
        const success = this.levelManager.loadNextLevel();
        if (!success) {
            console.log('No more levels available');
        }
    }

    /**
     * Handle retry level request
     */
    private onRetryLevelRequested(): void {
        console.log('Retry level requested');

        // Stop all audio before transitioning
        this.audioManager.prepareForSceneTransition();

        // Reset game state
        this.resetGameState();

        // Restart current level
        this.levelManager.restartLevel();
    }

    /**
     * Handle game reset request
     */
    private onGameResetRequested(): void {
        this.resetGameState();
    }

    /**
     * Handle all levels completed
     */
    private onAllLevelsCompleted(): void {
        console.log('All levels completed! Congratulations!');

        // Show completion modal using EndLevelModal
        this.endLevelModal.show({
            isVictory: true,
            levelName: 'All Levels Completed!',
            message: 'Congratulations! You have completed all available levels!',
            showNextButton: false, // No next level available
            showRetryButton: false, // No retry needed
            stars: 3 // Max stars for completion
        });
    }

    /**
     * Reset game state for new level
     */
    private resetGameState(): void {
        // Reset score manager
        this.scoreManager.resetGame();

        // Reset game board
        if (this.gameBoard) {
            this.gameBoard.resetBoard();
        }

        // Reset UI
        this.uiManager.resetUI();

        // Reset power-ups
        if (this.powerUpManager) {
            this.powerUpManager.resetPowerUps();
        }

        // Reset power-up collection system
        if (this.powerUpCollectionSystem) {
            this.powerUpCollectionSystem.resetSession();
        }

        // Reset objective manager
        this.objectiveManager.reset();
    }

    shutdown(): void {
        if (this.gameBoard) {
            this.gameBoard.destroy();
        }
        if (this.scoreManager) {
            this.scoreManager.destroy();
        }
        if (this.levelManager) {
            this.levelManager.destroy();
        }
        if (this.objectiveManager) {
            this.objectiveManager.destroy();
        }
        if (this.uiManager) {
            this.uiManager.destroy();
        }
        if (this.particleManager) {
            this.particleManager.destroy();
        }
        if (this.powerUpManager) {
            this.powerUpManager.destroy();
        }
        if (this.powerUpPanel) {
            this.powerUpPanel.destroy();
        }
        if (this.powerUpCollectionSystem) {
            this.powerUpCollectionSystem.destroy();
        }
        if (this.endLevelModal) {
            this.endLevelModal.destroy();
        }

        // Clean up level progression handlers
        this.events.off('level-started', this.onLevelStarted, this);
        this.events.off('next-level-requested', this.onNextLevelRequested, this);
        this.events.off('retry-level-requested', this.onRetryLevelRequested, this);
        this.events.off('game-reset-requested', this.onGameResetRequested, this);
        this.events.off('all-levels-completed', this.onAllLevelsCompleted, this);
    }
}
