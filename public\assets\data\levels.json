{"levels": [{"id": 1, "name": "Getting Started", "board": {"boardWidth": 6, "boardHeight": 6, "boardShape": "square", "stoneOverlays": {"enabled": true, "chance": 0.25, "maxCount": 3}}, "objectives": [{"type": "collect", "gemType": "blue", "count": 6}], "moveLimit": 15, "starThresholds": {"one": 1000, "two": 2500, "three": 4500}}, {"id": 2, "name": "Color Collector", "board": {"boardWidth": 4, "boardHeight": 4, "boardShape": "square", "obstacles": [{"row": 1, "col": 1, "type": "stone"}, {"row": 2, "col": 2, "type": "wood"}]}, "objectives": [{"type": "collect", "gemType": "green", "count": 12}, {"type": "collect", "gemType": "yellow", "count": 10}, {"type": "collect", "gemType": "purple", "count": 8}], "moveLimit": 20, "starThresholds": {"one": 2000, "two": 4500, "three": 7500}}, {"id": 3, "name": "Rainbow Challenge", "board": {"boardWidth": 5, "boardHeight": 5, "boardShape": "hexagon", "stoneOverlays": {"enabled": true, "chance": 0.2, "maxCount": 4}}, "objectives": [{"type": "collect", "gemType": "red", "count": 10}, {"type": "collect", "gemType": "blue", "count": 10}, {"type": "collect", "gemType": "green", "count": 10}, {"type": "collect", "gemType": "yellow", "count": 10}], "moveLimit": 25, "starThresholds": {"one": 3000, "two": 6500, "three": 11000}}, {"id": 4, "name": "Gem Master", "board": {"boardWidth": 6, "boardHeight": 5, "boardShape": "rectangle", "obstacles": [{"row": 2, "col": 2, "type": "ice"}, {"row": 2, "col": 3, "type": "metal"}, {"row": 1, "col": 4, "type": "stone"}, {"row": 3, "col": 1, "type": "wood"}], "stoneOverlays": {"enabled": true, "specificPositions": [{"row": 0, "col": 1}, {"row": 0, "col": 4}, {"row": 4, "col": 0}, {"row": 4, "col": 5}, {"row": 2, "col": 0}, {"row": 2, "col": 5}]}}, "objectives": [{"type": "collect", "gemType": "purple", "count": 18}, {"type": "collect", "gemType": "orange", "count": 15}, {"type": "collect", "gemType": "white", "count": 12}, {"type": "destroy-stone-overlays", "count": 6}], "moveLimit": 30, "starThresholds": {"one": 4000, "two": 8500, "three": 14000}}, {"id": 5, "name": "Ultimate Challenge", "board": {"boardWidth": 8, "boardHeight": 6, "boardShape": "triangle", "stoneOverlays": {"enabled": true, "chance": 0.35, "maxCount": 8}}, "objectives": [{"type": "collect", "gemType": "red", "count": 15}, {"type": "collect", "gemType": "blue", "count": 15}, {"type": "collect", "gemType": "green", "count": 15}, {"type": "collect", "gemType": "yellow", "count": 15}, {"type": "collect", "gemType": "purple", "count": 15}], "moveLimit": 40, "starThresholds": {"one": 6000, "two": 13000, "three": 22000}}]}