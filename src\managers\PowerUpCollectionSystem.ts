import { PowerUpType, PowerUpUtils } from '../types/PowerUpTypes';
import { PowerUpManager } from './PowerUpManager';

/**
 * System để collect power-ups từ gameplay
 */
export class PowerUpCollectionSystem {
    private scene: Phaser.Scene;
    private powerUpManager: PowerUpManager;
    private comboCount: number = 0;
    private totalMatches: number = 0;
    private sessionScore: number = 0;

    // Thresholds cho earning power-ups
    private readonly COMBO_THRESHOLDS = {
        [PowerUpType.HINT]: 2,        // 2 combo (easy to get)
        [PowerUpType.HAMMER]: 3,      // 3 combo
        [PowerUpType.BOMB]: 5,        // 5 combo
        [PowerUpType.LIGHTNING]: 7,   // 7 combo
        [PowerUpType.COLOR_BLAST]: 10, // 10 combo
        [PowerUpType.SHUFFLE]: 15     // 15 combo (rare)
    };

    private readonly MATCH_THRESHOLDS = {
        [PowerUpType.HINT]: 10,       // 10 total matches
        [PowerUpType.HAMMER]: 20,     // 20 total matches
        [PowerUpType.BOMB]: 50,       // 50 total matches
        [PowerUpType.LIGHTNING]: 100, // 100 total matches
        [PowerUpType.COLOR_BLAST]: 150, // 150 total matches
        [PowerUpType.SHUFFLE]: 200    // 200 total matches
    };

    private readonly SCORE_THRESHOLDS = {
        [PowerUpType.HINT]: 500,       // 500 score
        [PowerUpType.HAMMER]: 1000,    // 1k score
        [PowerUpType.BOMB]: 5000,      // 5k score
        [PowerUpType.LIGHTNING]: 15000, // 15k score
        [PowerUpType.COLOR_BLAST]: 30000, // 30k score
        [PowerUpType.SHUFFLE]: 50000   // 50k score
    };

    constructor(scene: Phaser.Scene, powerUpManager: PowerUpManager) {
        this.scene = scene;
        this.powerUpManager = powerUpManager;
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.on('combo-achieved', this.onComboAchieved, this);
        this.scene.events.on('score-updated', this.onScoreUpdated, this);
        this.scene.events.on('special-gem-created', this.onSpecialGemCreated, this);
    }

    /**
     * Handle matches processed
     */
    private onMatchesProcessed(data: { matches: any[], isCombo: boolean }): void {
        this.totalMatches += data.matches.length;
        
        // Check match-based rewards
        this.checkMatchBasedRewards();
        
        // Reset combo if not a combo
        if (!data.isCombo) {
            this.comboCount = 0;
        }
    }

    /**
     * Handle combo achieved
     */
    private onComboAchieved(data: { comboCount: number, totalScore: number }): void {
        this.comboCount = data.comboCount;
        
        // Check combo-based rewards
        this.checkComboBasedRewards();
        
        // Show combo feedback
        this.showComboFeedback(data.comboCount);
    }

    /**
     * Handle score updated
     */
    private onScoreUpdated(data: { score: number, totalScore: number }): void {
        this.sessionScore = data.totalScore;
        
        // Check score-based rewards
        this.checkScoreBasedRewards();
    }

    /**
     * Handle special gem created
     */
    private onSpecialGemCreated(data: { gem: any, type: string }): void {
        // Bonus power-up chance when creating special gems
        if (Math.random() < 0.3) { // 30% chance
            const randomPowerUp = this.getRandomPowerUp();
            this.awardPowerUp(randomPowerUp, 'Special Gem Bonus!');
        }
    }

    /**
     * Check combo-based rewards
     */
    private checkComboBasedRewards(): void {
        Object.entries(this.COMBO_THRESHOLDS).forEach(([powerUpType, threshold]) => {
            if (this.comboCount >= threshold && this.comboCount % threshold === 0) {
                this.awardPowerUp(powerUpType as PowerUpType, `${threshold} Combo Achieved!`);
            }
        });
    }

    /**
     * Check match-based rewards
     */
    private checkMatchBasedRewards(): void {
        Object.entries(this.MATCH_THRESHOLDS).forEach(([powerUpType, threshold]) => {
            if (this.totalMatches >= threshold && this.totalMatches % threshold === 0) {
                this.awardPowerUp(powerUpType as PowerUpType, `${threshold} Matches Milestone!`);
            }
        });
    }

    /**
     * Check score-based rewards
     */
    private checkScoreBasedRewards(): void {
        Object.entries(this.SCORE_THRESHOLDS).forEach(([powerUpType, threshold]) => {
            if (this.sessionScore >= threshold && this.sessionScore % threshold < 1000) {
                this.awardPowerUp(powerUpType as PowerUpType, `${threshold} Points Achieved!`);
            }
        });
    }

    /**
     * Award power-up to player
     */
    private awardPowerUp(type: PowerUpType, reason: string): void {
        this.powerUpManager.addPowerUp(type, 1);
        
        // Show award notification
        this.showPowerUpAward(type, reason);
        
        // Emit event for UI updates
        this.scene.events.emit('power-up-awarded', { type, reason });
    }

    /**
     * Show power-up award notification
     */
    private showPowerUpAward(type: PowerUpType, reason: string): void {
        const centerX = this.scene.cameras.main.centerX;
        const centerY = this.scene.cameras.main.centerY;

        // Create award container
        const container = this.scene.add.container(centerX, centerY - 100);
        container.setDepth(2000);

        // Background
        const bg = this.scene.add.rectangle(0, 0, 300, 80, 0x000000, 0.8);
        bg.setStrokeStyle(2, 0xffff00);
        container.add(bg);

        // Power-up icon với custom texture and atlas support
        const { texture, frame } = PowerUpUtils.getPowerUpTextureWithAtlas(this.scene, type);
        const icon = this.scene.add.sprite(-120, 0, texture, frame);
        icon.setScale(0.5); // Reduced from 1.2 to 0.5 to fit better in notification box
        container.add(icon);

        // Text
        const titleText = this.scene.add.text(0, -15, `${type.toUpperCase()} EARNED!`, {
            fontSize: '16px',
            color: '#ffff00',
            fontStyle: 'bold'
        });
        titleText.setOrigin(0.5);
        container.add(titleText);

        const reasonText = this.scene.add.text(0, 10, reason, {
            fontSize: '12px',
            color: '#ffffff'
        });
        reasonText.setOrigin(0.5);
        container.add(reasonText);

        // Animation
        container.setScale(0);
        this.scene.tweens.add({
            targets: container,
            scaleX: 1,
            scaleY: 1,
            duration: 300,
            ease: 'Back.easeOut',
            onComplete: () => {
                // Hold for a moment
                this.scene.time.delayedCall(2000, () => {
                    // Fade out
                    this.scene.tweens.add({
                        targets: container,
                        alpha: 0,
                        y: container.y - 50,
                        duration: 500,
                        ease: 'Power2.easeOut',
                        onComplete: () => {
                            container.destroy();
                        }
                    });
                });
            }
        });

        // Enhanced particle effects
        const particles = this.scene.add.particles(centerX, centerY - 100, 'particle', {
            speed: { min: 100, max: 250 },
            scale: { start: 0.8, end: 0 },
            alpha: { start: 1, end: 0 },
            lifespan: 1500,
            quantity: 30,
            angle: { min: 0, max: 360 },
            tint: this.getPowerUpColor(type),
            blendMode: 'ADD'
        });

        // Secondary sparkle effect
        const sparkles = this.scene.add.particles(centerX, centerY - 100, 'particle', {
            speed: { min: 50, max: 120 },
            scale: { start: 0.3, end: 0 },
            alpha: { start: 0.8, end: 0 },
            lifespan: 2000,
            quantity: 15,
            angle: { min: 0, max: 360 },
            tint: [0xffffff, this.getPowerUpColor(type)],
            blendMode: 'ADD',
            gravityY: -50
        });

        // Screen flash
        const flash = this.scene.add.rectangle(
            this.scene.cameras.main.centerX,
            this.scene.cameras.main.centerY,
            this.scene.cameras.main.width,
            this.scene.cameras.main.height,
            this.getPowerUpColor(type),
            0.1
        );
        flash.setDepth(1500);

        this.scene.tweens.add({
            targets: flash,
            alpha: 0,
            duration: 300,
            ease: 'Power2.easeOut',
            onComplete: () => flash.destroy()
        });

        this.scene.time.delayedCall(2000, () => {
            particles.destroy();
            sparkles.destroy();
        });
    }

    /**
     * Show combo feedback
     */
    private showComboFeedback(comboCount: number): void {
        if (comboCount < 3) return; // Only show for 3+ combos

        const centerX = this.scene.cameras.main.centerX;
        const centerY = this.scene.cameras.main.centerY;

        const comboText = this.scene.add.text(centerX, centerY - 50, `${comboCount}x COMBO!`, {
            fontSize: `${Math.min(48 + comboCount * 4, 72)}px`,
            color: '#ff4444',
            fontStyle: 'bold',
            stroke: '#000000',
            strokeThickness: 4
        });
        comboText.setOrigin(0.5);
        comboText.setDepth(1500);

        // Animation
        comboText.setScale(0);
        this.scene.tweens.add({
            targets: comboText,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 200,
            ease: 'Back.easeOut',
            onComplete: () => {
                this.scene.tweens.add({
                    targets: comboText,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 100,
                    onComplete: () => {
                        this.scene.time.delayedCall(1000, () => {
                            this.scene.tweens.add({
                                targets: comboText,
                                alpha: 0,
                                y: comboText.y - 30,
                                duration: 500,
                                onComplete: () => {
                                    comboText.destroy();
                                }
                            });
                        });
                    }
                });
            }
        });
    }

    /**
     * Get random power-up type
     */
    private getRandomPowerUp(): PowerUpType {
        const powerUps = [
            PowerUpType.HINT,
            PowerUpType.HAMMER,
            PowerUpType.BOMB,
            PowerUpType.LIGHTNING,
            PowerUpType.COLOR_BLAST,
            PowerUpType.SHUFFLE
        ];
        
        return powerUps[Math.floor(Math.random() * powerUps.length)];
    }

    /**
     * Get power-up color
     */
    private getPowerUpColor(type: PowerUpType): number {
        const colors: Record<PowerUpType, number> = {
            [PowerUpType.HINT]: 0x44ffff,
            [PowerUpType.BOMB]: 0xff4444,
            [PowerUpType.LIGHTNING]: 0xffff44,
            [PowerUpType.HAMMER]: 0x888888,
            [PowerUpType.SHUFFLE]: 0x44ff44,
            [PowerUpType.COLOR_BLAST]: 0xff44ff
        };
        
        return colors[type];
    }

    /**
     * Reset session stats
     */
    public resetSession(): void {
        this.comboCount = 0;
        this.totalMatches = 0;
        this.sessionScore = 0;
    }



    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('matches-processed', this.onMatchesProcessed, this);
        this.scene.events.off('combo-achieved', this.onComboAchieved, this);
        this.scene.events.off('score-updated', this.onScoreUpdated, this);
        this.scene.events.off('special-gem-created', this.onSpecialGemCreated, this);
    }
}
