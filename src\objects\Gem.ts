import { GemType, SpecialGemType, SpecialGemProperties, GemTypeUtils } from '../types/GemTypes';

/**
 * Class đại diện cho một viên gem trong game
 */
export class Gem extends Phaser.GameObjects.Sprite {
    public gemType: GemType;
    public gridX: number;
    public gridY: number;
    public isSelected: boolean = false;
    public isMatched: boolean = false;
    public isMoving: boolean = false;
    public specialType: SpecialGemType = SpecialGemType.NONE;
    public specialProperties?: SpecialGemProperties;

    // Stone Overlay properties
    private _hasStoneOverlay: boolean = false;
    private stoneOverlaySprite?: Phaser.GameObjects.Sprite;

    private selectionGlow?: Phaser.GameObjects.Sprite;
    private specialGlow?: Phaser.GameObjects.Sprite;
    
    constructor(scene: Phaser.Scene, x: number, y: number, gemType: GemType, gridX: number, gridY: number, specialType: SpecialGemType = SpecialGemType.NONE, hasStoneOverlay: boolean = false) {
        // Check if atlas is available
        const atlasAvailable = scene.registry.get('atlasAvailable') && scene.textures.exists('game_atlas');

        let texture: string;
        let frame: string | undefined;

        if (atlasAvailable) {
            // Use atlas frames
            texture = 'game_atlas';
            frame = GemTypeUtils.getGemTexture(gemType, specialType);
        } else {
            // Use individual textures (fallback)
            texture = GemTypeUtils.getGemTexture(gemType, specialType);
            frame = undefined;
        }

        super(scene, x, y, texture, frame);

        this.gemType = gemType;
        this.gridX = gridX;
        this.gridY = gridY;
        this.specialType = specialType;
        this._hasStoneOverlay = hasStoneOverlay;

        // Setup special properties if this is a special gem
        if (GemTypeUtils.isSpecialGem(specialType)) {
            this.setupSpecialProperties();
        }

        // Thêm vào scene
        scene.add.existing(this);

        // Set interactive
        this.setInteractive();
        this.setupInteractions();

        // Scale để fit grid (60px cell, 128px image -> 0.45 scale = 57.6px)
        this.setScale(0.45);
        this.setOrigin(0.5);

        // Add special visual effects if needed
        if (GemTypeUtils.isSpecialGem(specialType)) {
            this.addSpecialVisualEffects();
        }

        // Add stone overlay if specified
        if (hasStoneOverlay) {
            this.addStoneOverlay();
        }
    }
    
    /**
     * Setup các tương tác chuột/touch với enhanced effects
     */
    private setupInteractions(): void {
        // Enhanced hover effects
        this.on('pointerover', () => {
            if (!this.isSelected && !this.isMoving) {
                // Subtle glow effect
                this.setTint(0xdddddd);

                // Gentle scale up
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.50,
                    scaleY: 0.50,
                    duration: 150,
                    ease: 'Power2.easeOut'
                });
            }
        });

        this.on('pointerout', () => {
            if (!this.isSelected && !this.isMoving) {
                this.clearTint();

                // Scale back to normal
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.45,
                    scaleY: 0.45,
                    duration: 150,
                    ease: 'Power2.easeOut'
                });
            }
        });

        // Enhanced click feedback
        this.on('pointerdown', () => {
            if (!this.isMoving) {
                // Quick scale down for tactile feedback
                this.scene.tweens.add({
                    targets: this,
                    scaleX: 0.40,
                    scaleY: 0.40,
                    duration: 100,
                    ease: 'Power2.easeOut',
                    yoyo: true
                });

                this.scene.events.emit('gem-clicked', this);
            }
        });
    }
    
    /**
     * Hiển thị selection
     */
    public select(): void {
        if (this.isSelected) return;
        
        this.isSelected = true;
        
        // Tạo glow effect
        this.selectionGlow = this.scene.add.sprite(this.x, this.y, 'selection');
        this.selectionGlow.setDepth(this.depth - 1);
        
        // Enhanced pulsing animation
        this.scene.tweens.add({
            targets: this.selectionGlow,
            scaleX: 1.3,
            scaleY: 1.3,
            alpha: 0.8,
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        // Gem scale up with bounce
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.54,
            scaleY: 0.54,
            duration: 300,
            ease: 'Elastic.easeOut'
        });
    }
    
    /**
     * Bỏ selection
     */
    public deselect(): void {
        if (!this.isSelected) return;
        
        this.isSelected = false;
        this.clearTint();
        
        // Remove glow
        if (this.selectionGlow) {
            this.scene.tweens.killTweensOf(this.selectionGlow);
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }
        
        // Reset scale
        this.scene.tweens.add({
            targets: this,
            scaleX: 0.45,
            scaleY: 0.45,
            duration: 200,
            ease: 'Back.easeOut'
        });
    }
    
    /**
     * Di chuyển gem đến vị trí mới với enhanced animation
     */
    public moveTo(newGridX: number, newGridY: number, worldX: number, worldY: number, duration: number = 300): Promise<void> {
        return new Promise((resolve) => {
            this.isMoving = true;
            this.gridX = newGridX;
            this.gridY = newGridY;

            // Emit gem moving event for particle trails
            this.scene.events.emit('gem-moving', this);

            // Determine animation type based on movement
            const isVerticalDrop = worldY > this.y;
            const isSwap = Math.abs(worldX - this.x) > 0 && Math.abs(worldY - this.y) > 0;

            let easeType = 'Power2.easeOut';
            let actualDuration = duration;

            if (isVerticalDrop) {
                // Falling gems get bounce effect
                easeType = 'Bounce.easeOut';
                actualDuration = Math.min(duration + 200, 600);
            } else if (isSwap) {
                // Swap animations are smoother
                easeType = 'Back.easeInOut';
                actualDuration = 250;
            }

            // Animate gem
            this.scene.tweens.add({
                targets: this,
                x: worldX,
                y: worldY,
                duration: actualDuration,
                ease: easeType,
                onComplete: () => {
                    this.isMoving = false;
                    resolve();
                }
            });

            // Animate stone overlay if present
            if (this.stoneOverlaySprite) {
                this.scene.tweens.add({
                    targets: this.stoneOverlaySprite,
                    x: worldX,
                    y: worldY,
                    duration: actualDuration,
                    ease: easeType
                });
            }
        });
    }
    
    /**
     * Enhanced animation khi gem bị match
     */
    public playMatchAnimation(): Promise<void> {
        return new Promise((resolve) => {
            this.isMatched = true;

            // Multi-stage animation for more impact
            // Stage 1: Quick scale up with rotation
            this.scene.tweens.add({
                targets: this,
                scaleX: 1.0,
                scaleY: 1.0,
                rotation: 0.2,
                duration: 150,
                ease: 'Back.easeOut',
                onComplete: () => {
                    // Stage 2: Dramatic shrink and fade with spin
                    this.scene.tweens.add({
                        targets: this,
                        scaleX: 0,
                        scaleY: 0,
                        rotation: 1.5,
                        alpha: 0,
                        duration: 200,
                        ease: 'Power3.easeIn',
                        onComplete: () => {
                            resolve();
                        }
                    });
                }
            });

            // Enhanced particle effect
            this.createMatchParticles();

            // Add screen shake for impact
            this.scene.cameras.main.shake(100, 0.01);
        });
    }
    
    /**
     * Enhanced particle effects khi match
     */
    private createMatchParticles(): void {
        // Main explosion particles
        const explosionParticles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 80, max: 200 },
            scale: { start: 0.8, end: 0 },
            lifespan: 800,
            quantity: 12,
            angle: { min: 0, max: 360 },
            alpha: { start: 1, end: 0 },
            tint: this.getGemColor()
        });

        // Secondary sparkle particles
        const sparkleParticles = this.scene.add.particles(this.x, this.y, 'particle', {
            speed: { min: 30, max: 100 },
            scale: { start: 0.3, end: 0 },
            lifespan: 1000,
            quantity: 8,
            angle: { min: 0, max: 360 },
            alpha: { start: 0.8, end: 0 },
            tint: 0xffffff,
            blendMode: 'ADD'
        });

        // Cleanup particles
        this.scene.time.delayedCall(1000, () => {
            explosionParticles.destroy();
            sparkleParticles.destroy();
        });
    }

    /**
     * Get color tint based on gem type
     */
    private getGemColor(): number {
        return GemTypeUtils.getGemColor(this.gemType);
    }

    /**
     * Setup special properties cho special gems
     */
    private setupSpecialProperties(): void {
        this.specialProperties = {
            type: this.specialType,
            baseGemType: this.gemType,
            canActivate: true,
            activationRange: this.getActivationRange()
        };
    }

    /**
     * Get activation range cho special gem
     */
    private getActivationRange(): any {
        switch (this.specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
                return { horizontal: true, vertical: false };
            case SpecialGemType.STRIPED_VERTICAL:
                return { horizontal: false, vertical: true };
            case SpecialGemType.WRAPPED:
                return { radius: 1 };
            case SpecialGemType.COLOR_BOMB:
                return { radius: 999 }; // Affects all gems of same color
            default:
                return {};
        }
    }

    /**
     * Add special visual effects cho special gems
     */
    private addSpecialVisualEffects(): void {
        // Add glow effect for special gems
        this.specialGlow = this.scene.add.sprite(this.x, this.y, 'particle');
        this.specialGlow.setScale(1.2);
        this.specialGlow.setAlpha(0.6);
        this.specialGlow.setBlendMode(Phaser.BlendModes.ADD);
        this.specialGlow.setDepth(this.depth - 1);

        // Different colors for different special types
        switch (this.specialType) {
            case SpecialGemType.STRIPED_HORIZONTAL:
            case SpecialGemType.STRIPED_VERTICAL:
                this.specialGlow.setTint(0xffffff);
                break;
            case SpecialGemType.WRAPPED:
                this.specialGlow.setTint(0xffff00);
                break;
            case SpecialGemType.COLOR_BOMB:
                this.specialGlow.setTint(0xff00ff);
                break;
        }

        // Animate the glow
        this.scene.tweens.add({
            targets: this.specialGlow,
            alpha: { from: 0.3, to: 0.8 },
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    /**
     * Check if this is a special gem
     */
    public isSpecial(): boolean {
        return GemTypeUtils.isSpecialGem(this.specialType);
    }

    /**
     * Activate special gem power
     */
    public activateSpecialPower(): void {
        if (!this.isSpecial() || !this.specialProperties?.canActivate) {
            return;
        }

        // Emit event với special gem activation
        this.scene.events.emit('special-gem-activated', {
            gem: this,
            type: this.specialType,
            position: { x: this.gridX, y: this.gridY },
            range: this.specialProperties.activationRange
        });

        // Disable further activation
        this.specialProperties.canActivate = false;
    }

    /**
     * Check if gem has stone overlay
     */
    public hasStoneOverlay(): boolean {
        return this._hasStoneOverlay;
    }

    /**
     * Add stone overlay to gem
     */
    public addStoneOverlay(): void {
        if (this._hasStoneOverlay || this.stoneOverlaySprite) {
            return; // Already has overlay
        }

        this._hasStoneOverlay = true;

        // Create stone overlay sprite (giống obstacle stone)
        this.stoneOverlaySprite = this.scene.add.sprite(this.x, this.y, 'stone_overlay');
        this.stoneOverlaySprite.setScale(0.47); // Slightly larger than gem để cover hoàn toàn
        this.stoneOverlaySprite.setOrigin(0.5);
        this.stoneOverlaySprite.setAlpha(0.5); // Lower opacity để thấy rõ gem bên dưới
        this.stoneOverlaySprite.setDepth(this.depth + 1); // Render trên gem

        // Emit event for tracking
        this.scene.events.emit('stone-overlay-added', { gem: this });
    }

    /**
     * Remove stone overlay from gem với animation
     */
    public removeStoneOverlay(): Promise<void> {
        return new Promise((resolve) => {
            if (!this._hasStoneOverlay || !this.stoneOverlaySprite) {
                resolve();
                return;
            }

            // Play crack animation
            this.playStoneOverlayRemovalAnimation().then(() => {
                this._hasStoneOverlay = false;

                if (this.stoneOverlaySprite) {
                    this.stoneOverlaySprite.destroy();
                    this.stoneOverlaySprite = undefined;
                }

                // Emit event for tracking
                this.scene.events.emit('stone-overlay-removed', { gem: this });
                resolve();
            });
        });
    }

    /**
     * Play stone overlay removal animation
     */
    private playStoneOverlayRemovalAnimation(): Promise<void> {
        return new Promise((resolve) => {
            if (!this.stoneOverlaySprite) {
                resolve();
                return;
            }

            // Crack effect animation
            this.scene.tweens.add({
                targets: this.stoneOverlaySprite,
                scaleX: 1.2,
                scaleY: 1.2,
                alpha: 0,
                rotation: 0.3,
                duration: 300,
                ease: 'Power2.easeOut',
                onComplete: () => {
                    resolve();
                }
            });

            // Particle effects will be handled by ParticleManager via event
        });
    }


    
    /**
     * Reset gem về trạng thái ban đầu
     */
    public reset(): void {
        this.isSelected = false;
        this.isMatched = false;
        this.isMoving = false;
        this.setAlpha(1);
        this.setScale(0.45);
        this.clearTint();

        if (this.selectionGlow) {
            this.selectionGlow.destroy();
            this.selectionGlow = undefined;
        }

        // Reset special properties
        if (this.specialProperties) {
            this.specialProperties.canActivate = true;
        }

        // Reset stone overlay sprite scale if present
        if (this.stoneOverlaySprite) {
            this.stoneOverlaySprite.setScale(0.47);
            this.stoneOverlaySprite.setAlpha(0.5);
        }
    }

    /**
     * Destroy gem và cleanup special effects
     */
    public destroy(): void {
        if (this.specialGlow) {
            this.specialGlow.destroy();
        }

        if (this.selectionGlow) {
            this.selectionGlow.destroy();
        }

        // Cleanup stone overlay
        if (this.stoneOverlaySprite) {
            this.stoneOverlaySprite.destroy();
            this.stoneOverlaySprite = undefined;
        }

        super.destroy();
    }
}
