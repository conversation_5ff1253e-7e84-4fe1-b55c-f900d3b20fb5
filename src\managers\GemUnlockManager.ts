import { GemType, GemTypeUtils } from '../types/GemTypes';

/**
 * Interface cho gem unlock configuration
 */
export interface GemUnlockConfig {
    gemType: GemType;
    unlockLevel: number;
    name: string;
    description: string;
}

/**
 * Manager đ<PERSON> q<PERSON> lý unlock gems theo level progression
 */
export class GemUnlockManager {
    private static instance: GemUnlockManager;
    private unlockedGems: Set<GemType> = new Set();
    private gemUnlockConfigs: GemUnlockConfig[] = [];

    private constructor() {
        this.initializeUnlockConfigs();
        this.initializeBasicGems();
    }

    /**
     * Singleton pattern
     */
    public static getInstance(): GemUnlockManager {
        if (!GemUnlockManager.instance) {
            GemUnlockManager.instance = new GemUnlockManager();
        }
        return GemUnlockManager.instance;
    }

    /**
     * Initialize gem unlock configurations
     */
    private initializeUnlockConfigs(): void {
        this.gemUnlockConfigs = [
            // Level 6-8: Precious stones tier 1
            {
                gemType: GemType.DIAMOND,
                unlockLevel: 6,
                name: 'Diamond',
                description: 'Brilliant crystal gem that sparkles with pure light'
            },
            {
                gemType: GemType.RUBY,
                unlockLevel: 8,
                name: 'Ruby',
                description: 'Deep red precious stone with fiery passion'
            },
            
            // Level 9-11: Precious stones tier 2
            {
                gemType: GemType.EMERALD,
                unlockLevel: 9,
                name: 'Emerald',
                description: 'Vibrant green gem of nature and growth'
            },
            {
                gemType: GemType.SAPPHIRE,
                unlockLevel: 11,
                name: 'Sapphire',
                description: 'Royal blue stone of wisdom and nobility'
            },
            
            // Level 12-14: Mystical gems
            {
                gemType: GemType.CRYSTAL,
                unlockLevel: 12,
                name: 'Crystal',
                description: 'Mystical transparent gem with magical properties'
            },
            {
                gemType: GemType.MOONSTONE,
                unlockLevel: 14,
                name: 'Moonstone',
                description: 'Ethereal silver gem blessed by moonlight'
            },
            
            // Level 15-17: Dark & rare gems
            {
                gemType: GemType.OBSIDIAN,
                unlockLevel: 15,
                name: 'Obsidian',
                description: 'Volcanic black glass with mysterious power'
            },
            {
                gemType: GemType.FIRE_OPAL,
                unlockLevel: 17,
                name: 'Fire Opal',
                description: 'Blazing orange gem that burns with inner flame'
            },
            
            // Level 18-20: Elemental gems
            {
                gemType: GemType.ICE_CRYSTAL,
                unlockLevel: 18,
                name: 'Ice Crystal',
                description: 'Frozen blue crystal from the eternal winter'
            },
            {
                gemType: GemType.LIGHTNING,
                unlockLevel: 20,
                name: 'Lightning',
                description: 'Electric yellow gem crackling with storm energy'
            }
        ];
    }

    /**
     * Initialize basic gems (always unlocked)
     */
    private initializeBasicGems(): void {
        const basicGems = GemTypeUtils.getBasicGemTypes();
        basicGems.forEach(gemType => {
            this.unlockedGems.add(gemType);
        });
    }

    /**
     * Check if gem is unlocked
     */
    public isGemUnlocked(gemType: GemType): boolean {
        return this.unlockedGems.has(gemType);
    }

    /**
     * Get all unlocked gems
     */
    public getUnlockedGems(): GemType[] {
        return Array.from(this.unlockedGems);
    }

    /**
     * Get available gems for current level
     */
    public getAvailableGemsForLevel(currentLevel: number): GemType[] {
        // Always include basic gems
        const availableGems = [...GemTypeUtils.getBasicGemTypes()];
        
        // Add unlocked advanced gems
        this.gemUnlockConfigs.forEach(config => {
            if (currentLevel >= config.unlockLevel && this.isGemUnlocked(config.gemType)) {
                availableGems.push(config.gemType);
            }
        });
        
        return availableGems;
    }

    /**
     * Unlock gems for completed level
     */
    public unlockGemsForLevel(completedLevel: number): GemType[] {
        const newlyUnlocked: GemType[] = [];
        
        this.gemUnlockConfigs.forEach(config => {
            if (completedLevel >= config.unlockLevel && !this.isGemUnlocked(config.gemType)) {
                this.unlockedGems.add(config.gemType);
                newlyUnlocked.push(config.gemType);
            }
        });
        
        // Save to localStorage
        this.saveUnlockProgress();
        
        return newlyUnlocked;
    }

    /**
     * Get gems that will be unlocked at specific level
     */
    public getGemsUnlockedAtLevel(level: number): GemUnlockConfig[] {
        return this.gemUnlockConfigs.filter(config => config.unlockLevel === level);
    }

    /**
     * Get next unlock level
     */
    public getNextUnlockLevel(currentLevel: number): number | null {
        const nextUnlock = this.gemUnlockConfigs.find(config => 
            config.unlockLevel > currentLevel && !this.isGemUnlocked(config.gemType)
        );
        return nextUnlock ? nextUnlock.unlockLevel : null;
    }

    /**
     * Get unlock config for gem type
     */
    public getUnlockConfig(gemType: GemType): GemUnlockConfig | null {
        return this.gemUnlockConfigs.find(config => config.gemType === gemType) || null;
    }

    /**
     * Save unlock progress to localStorage
     */
    private saveUnlockProgress(): void {
        const unlockedArray = Array.from(this.unlockedGems);
        localStorage.setItem('match3_unlocked_gems', JSON.stringify(unlockedArray));
    }

    /**
     * Load unlock progress from localStorage
     */
    public loadUnlockProgress(): void {
        try {
            const saved = localStorage.getItem('match3_unlocked_gems');
            if (saved) {
                const unlockedArray = JSON.parse(saved) as GemType[];
                this.unlockedGems = new Set(unlockedArray);
                
                // Ensure basic gems are always unlocked
                this.initializeBasicGems();
            }
        } catch (error) {
            console.warn('Failed to load gem unlock progress:', error);
            // Reset to basic gems only
            this.unlockedGems.clear();
            this.initializeBasicGems();
        }
    }

    /**
     * Reset all unlock progress (for testing/debug)
     */
    public resetUnlockProgress(): void {
        this.unlockedGems.clear();
        this.initializeBasicGems();
        localStorage.removeItem('match3_unlocked_gems');
    }

    /**
     * Get unlock progress statistics
     */
    public getUnlockStats(): {
        totalGems: number;
        unlockedGems: number;
        basicGems: number;
        advancedGems: number;
        unlockedAdvanced: number;
    } {
        const basicGems = GemTypeUtils.getBasicGemTypes();
        const advancedGems = GemTypeUtils.getAdvancedGemTypes();
        const unlockedAdvanced = advancedGems.filter(gem => this.isGemUnlocked(gem));
        
        return {
            totalGems: basicGems.length + advancedGems.length,
            unlockedGems: this.unlockedGems.size,
            basicGems: basicGems.length,
            advancedGems: advancedGems.length,
            unlockedAdvanced: unlockedAdvanced.length
        };
    }
}
