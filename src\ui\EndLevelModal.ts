/**
 * EndLevelModal - Modal hiển thị kết quả level và options
 */
import { AudioManager } from '../managers/AudioManager';

export interface EndLevelResult {
    levelId: number;
    success: boolean;
    score: number;
    movesUsed: number;
    movesRemaining: number;
    reason?: string;
}

export class EndLevelModal {
    private scene: Phaser.Scene;
    private container?: Phaser.GameObjects.Container;
    private background?: Phaser.GameObjects.Rectangle;
    private isVisible: boolean = false;

    constructor(scene: Phaser.Scene) {
        this.scene = scene;
        this.setupEventListeners();
    }

    /**
     * Setup event listeners
     */
    private setupEventListeners(): void {
        this.scene.events.on('level-completed', this.onLevelCompleted, this);
        this.scene.events.on('level-failed', this.onLevelFailed, this);
    }

    /**
     * Handle level completed
     */
    private onLevelCompleted(data: { levelId: number, movesUsed: number, movesRemaining: number }): void {
        const result: EndLevelResult = {
            levelId: data.levelId,
            success: true,
            score: 0, // Will get from ScoreManager
            movesUsed: data.movesUsed,
            movesRemaining: data.movesRemaining
        };

        // Get current score from scene
        const scoreManager = (this.scene as any).scoreManager;
        if (scoreManager) {
            result.score = scoreManager.getCurrentScore();
        }

        this.showModal(result);
    }

    /**
     * Handle level failed
     */
    private onLevelFailed(data: { levelId: number, reason: string }): void {
        const result: EndLevelResult = {
            levelId: data.levelId,
            success: false,
            score: 0,
            movesUsed: 0,
            movesRemaining: 0,
            reason: data.reason
        };

        // Get current score from scene
        const scoreManager = (this.scene as any).scoreManager;
        if (scoreManager) {
            result.score = scoreManager.getCurrentScore();
        }

        this.showModal(result);
    }

    /**
     * Show modal với kết quả
     */
    public showModal(result: EndLevelResult): void {
        if (this.isVisible) return;

        this.isVisible = true;
        this.createModal(result);
    }

    /**
     * Create modal UI
     */
    private createModal(result: EndLevelResult): void {
        const { width, height } = this.scene.cameras.main;

        // Create container
        this.container = this.scene.add.container(width / 2, height / 2);
        this.container.setDepth(2000);

        // Create background overlay
        const overlay = this.scene.add.rectangle(0, 0, width, height, 0x000000, 0.7);
        overlay.setOrigin(0.5);
        this.container.add(overlay);

        // Create modal background
        this.background = this.scene.add.rectangle(0, 0, 400, 300, 0x1a1a2e, 0.95);
        this.background.setStrokeStyle(3, 0x16213e);
        this.container.add(this.background);

        // Add glow effect
        const glow = this.scene.add.rectangle(0, 0, 406, 306, 0x0f3460, 0.3);
        this.container.add(glow);

        // Create content based on result
        if (result.success) {
            this.createSuccessContent(result);
        } else {
            this.createFailureContent(result);
        }

        // Animate modal in
        this.container.setScale(0);
        this.scene.tweens.add({
            targets: this.container,
            scaleX: 1,
            scaleY: 1,
            duration: 300,
            ease: 'Back.easeOut'
        });
    }

    /**
     * Create success content
     */
    private createSuccessContent(result: EndLevelResult): void {
        if (!this.container) return;

        // Title
        const title = this.scene.add.text(0, -100, 'LEVEL COMPLETE!', {
            fontSize: '28px',
            fontFamily: 'Arial, sans-serif',
            color: '#00ff00',
            stroke: '#000000',
            strokeThickness: 2
        });
        title.setOrigin(0.5);
        this.container.add(title);

        // Level info
        const levelText = this.scene.add.text(0, -60, `Level ${result.levelId}`, {
            fontSize: '20px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 1
        });
        levelText.setOrigin(0.5);
        this.container.add(levelText);

        // Score
        const scoreText = this.scene.add.text(0, -20, `Score: ${result.score}`, {
            fontSize: '18px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffff00',
            stroke: '#000000',
            strokeThickness: 1
        });
        scoreText.setOrigin(0.5);
        this.container.add(scoreText);

        // Moves info
        const movesText = this.scene.add.text(0, 10, `Moves used: ${result.movesUsed}`, {
            fontSize: '16px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 1
        });
        movesText.setOrigin(0.5);
        this.container.add(movesText);

        // Buttons
        this.createButtons(result);
    }

    /**
     * Create failure content
     */
    private createFailureContent(result: EndLevelResult): void {
        if (!this.container) return;

        // Title
        const title = this.scene.add.text(0, -100, 'LEVEL FAILED', {
            fontSize: '28px',
            fontFamily: 'Arial, sans-serif',
            color: '#ff0000',
            stroke: '#000000',
            strokeThickness: 2
        });
        title.setOrigin(0.5);
        this.container.add(title);

        // Reason
        let reasonText = 'Try again!';
        if (result.reason === 'out_of_moves') {
            reasonText = 'Out of moves!';
        }

        const reason = this.scene.add.text(0, -60, reasonText, {
            fontSize: '20px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 1
        });
        reason.setOrigin(0.5);
        this.container.add(reason);

        // Score
        const scoreText = this.scene.add.text(0, -20, `Score: ${result.score}`, {
            fontSize: '18px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffff00',
            stroke: '#000000',
            strokeThickness: 1
        });
        scoreText.setOrigin(0.5);
        this.container.add(scoreText);

        // Buttons (only retry for failed levels)
        this.createFailureButtons(result);
    }

    /**
     * Create success buttons
     */
    private createButtons(result: EndLevelResult): void {
        if (!this.container) return;

        // Next Level button
        const nextButton = this.createButton(-80, 80, 'NEXT', 0x00aa00, () => {
            this.hideModal();
            this.scene.events.emit('next-level-requested');
        });
        this.container.add(nextButton);

        // Retry button
        const retryButton = this.createButton(80, 80, 'RETRY', 0x0066aa, () => {
            this.hideModal();
            this.scene.events.emit('retry-level-requested');
        });
        this.container.add(retryButton);
    }

    /**
     * Create failure buttons
     */
    private createFailureButtons(result: EndLevelResult): void {
        if (!this.container) return;

        // Retry button
        const retryButton = this.createButton(0, 80, 'RETRY', 0x0066aa, () => {
            this.hideModal();
            this.scene.events.emit('retry-level-requested');
        });
        this.container.add(retryButton);
    }

    /**
     * Create button helper
     */
    private createButton(x: number, y: number, text: string, color: number, callback: () => void): Phaser.GameObjects.Container {
        const buttonContainer = this.scene.add.container(x, y);

        // Button background
        const bg = this.scene.add.rectangle(0, 0, 120, 40, color);
        bg.setStrokeStyle(2, 0xffffff);
        bg.setInteractive();
        buttonContainer.add(bg);

        // Button text
        const buttonText = this.scene.add.text(0, 0, text, {
            fontSize: '16px',
            fontFamily: 'Arial, sans-serif',
            color: '#ffffff',
            stroke: '#000000',
            strokeThickness: 1
        });
        buttonText.setOrigin(0.5);
        buttonContainer.add(buttonText);

        // Hover effects
        bg.on('pointerover', () => {
            bg.setFillStyle(color + 0x222222);
            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 1.1,
                scaleY: 1.1,
                duration: 100
            });
        });

        bg.on('pointerout', () => {
            bg.setFillStyle(color);
            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 1,
                scaleY: 1,
                duration: 100
            });
        });

        bg.on('pointerdown', () => {
            // Play button click sound
            const audioManager = AudioManager.getInstance();
            if (audioManager) {
                audioManager.playSound('button_click');
            }

            this.scene.tweens.add({
                targets: buttonContainer,
                scaleX: 0.95,
                scaleY: 0.95,
                duration: 50,
                yoyo: true,
                onComplete: callback
            });
        });

        return buttonContainer;
    }

    /**
     * Hide modal
     */
    public hideModal(): void {
        if (!this.isVisible || !this.container) return;

        this.scene.tweens.add({
            targets: this.container,
            scaleX: 0,
            scaleY: 0,
            duration: 200,
            ease: 'Back.easeIn',
            onComplete: () => {
                if (this.container) {
                    this.container.destroy();
                    this.container = undefined;
                }
                this.isVisible = false;
            }
        });
    }

    /**
     * Cleanup
     */
    public destroy(): void {
        this.scene.events.off('level-completed', this.onLevelCompleted, this);
        this.scene.events.off('level-failed', this.onLevelFailed, this);

        if (this.container) {
            this.container.destroy();
        }
    }
}
